import {
  buyAgent,
  closeDCAOrder,
  getAllAggregatedActivities,
  getDCAOrders,
  getSnipeAndDCAActivities,
  getSnipeOrder,
  swap,
  transfer,
} from '@/controllers/transaction/coin';
import performNFTTransaction from '@/controllers/transaction/nft';
import {
  closeSnipeOrder,
  getOpenSnipeOrders,
  placeSnipeOrder,
} from '@/controllers/transaction/snipe';
import {
  closeTradePosition,
  getOpenDCAOrders,
  getRealizedPnl,
  getTradePositions,
  openLimitPosition,
  openMarketPosition,
  syncTradeHistory,
} from '@/controllers/transaction/trade';
import authenticateToken from '@/middlewares/auth';
import { Router, Request, Response } from 'express';

const router = Router();

router.post('/nft-txn', authenticateToken, performNFTTransaction);
router.post('/coin-txn', authenticateToken, swap);
router.post('/buy-agent', authenticateToken, buyAgent);
router.post('/transfer', authenticateToken, transfer);
router.post('/open-market-order', authenticateToken, openMarketPosition);
router.post('/open-limit-order', authenticateToken, openLimitPosition);
router.post('/close-position', authenticateToken, closeTradePosition);
router.get('/get-positions', authenticateToken, getTradePositions);

// sync trades
router.post('/sync-trades', authenticateToken, syncTradeHistory);
router.get('/realized-pnl', authenticateToken, getRealizedPnl);

// get open positions
router.get('/dca-orders', authenticateToken, getOpenDCAOrders);
router.get('/snipe-orders', authenticateToken, getOpenSnipeOrders);
router.get('/dca-orders/:order_id', authenticateToken, getDCAOrders);
router.get('/snipe-orders/:order_id', authenticateToken, getSnipeOrder);

// place snipe order
router.post('/snipe-order', authenticateToken, placeSnipeOrder);

// remove orders
router.delete('/snipe-orders/:id', authenticateToken, closeSnipeOrder);
router.delete('/dca-orders/:id', authenticateToken, closeDCAOrder);

// activities
router.get('/activities', authenticateToken, getSnipeAndDCAActivities);
router.get('/activities/all', getAllAggregatedActivities);

export default router;
