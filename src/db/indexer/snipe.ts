import indexerDbPool from '@/config/indexerDb';
import { getAssetUsdPrice } from '@/utils/blockchain/transaction/coinTxnBuilder';
import { readFileSync } from 'fs';
import path from 'path';

export interface SnipeOrderConfig {
  target_token_address: string;
  base_token_address: string;
  reference_price: number;
  price_change_threshold: number;
  direction: 'up' | 'down';
  buy_amount: number;
  polling_interval: number;
  slippage_tolerance: number;
  user_wallet_address: string;
  agent_wallet_address: string;
  take_profit_percent?: number;
  stop_loss_percent?: number;
}

export interface SnipeOrderRow {
  id: number;
  target_token_address: string;
  base_token_address: string;
  price_change_threshold: number;
  reference_price: number;
  direction: 'up' | 'down';
  buy_amount: number;
  target_token_amount: number;
  polling_interval: number;
  slippage_tolerance: number;
  user_wallet_address: string;
  agent_wallet_address: string;
  take_profit_percent?: number;
  stop_loss_percent?: number;
  closed: boolean;
  executed: boolean;
  tp_price: number;
  created_at: string;
  updated_at: string;
}

interface Token {
  chainId: number;
  tokenAddress: string;
  faAddress: string;
  name: string;
  symbol: string;
  decimals: number;
  logoUrl: string;
  websiteUrl: string;
}

export interface AgentSwapActivity {
  success: boolean;
  order_id: number | null;
  source: 'NORMAL' | 'DCA' | 'SNIPE' | 'SNIPE-TP';
  sender: string;
  transaction_hash: string | null;
  from_token: string;
  to_token: string;
  to_token_price_usd: number;
  from_amount: number;
  to_amount: number;
  transaction_timestamp: Date | string;
  created_at?: Date;
}

export function getTokenDecimals(address: string): number | null {
  const filePath = path.join(__dirname, 'token-list.json');
  const fileContent = readFileSync(filePath, 'utf-8');
  const tokens: Token[] = JSON.parse(fileContent);

  const token = tokens.find(t => t.tokenAddress == address || t.faAddress == address);

  return token ? (token.decimals as number) : null;
}

export async function insertSnipeOrder(config: SnipeOrderConfig) {
  const {
    target_token_address,
    base_token_address,
    reference_price,
    price_change_threshold,
    direction,
    buy_amount,
    polling_interval,
    slippage_tolerance,
    user_wallet_address,
    agent_wallet_address,
    take_profit_percent,
    stop_loss_percent,
  } = config;

  const conflictCheckQuery = `
    SELECT 1 FROM alura.snipe_orders
    WHERE target_token_address = $1
      AND agent_wallet_address = $2
      AND closed = false
    LIMIT 1
  `;

  const conflictValues = [target_token_address, agent_wallet_address];

  const conflictResult = await indexerDbPool.query(conflictCheckQuery, conflictValues);
  if (conflictResult.rowCount) {
    throw new Error(`Snipe order already exists for target token ${target_token_address}`);
  }

  const query = `
    INSERT INTO alura.snipe_orders (
      target_token_address,
      base_token_address,
      reference_price,
      price_change_threshold,
      direction,
      buy_amount,
      polling_interval,
      slippage_tolerance,
      user_wallet_address,
      agent_wallet_address,
      take_profit_percent,
      stop_loss_percent
    ) VALUES (
      $1, $2, $3, $4, $5,
      $6, $7, $8, $9, $10, $11, $12
    )
  `;

  const values = [
    target_token_address,
    base_token_address,
    reference_price,
    price_change_threshold,
    direction,
    buy_amount,
    polling_interval,
    slippage_tolerance,
    user_wallet_address,
    agent_wallet_address,
    take_profit_percent ?? null,
    stop_loss_percent ?? null,
  ];

  try {
    return await indexerDbPool.query(query, values);
  } catch (error) {
    console.error('Error inserting snipe order:', error);
    throw error;
  }
}

export async function getBuyOrdersWithThreshold(threshold: number): Promise<SnipeOrderRow[]> {
  const query = `
    SELECT *
    FROM snipe_orders
    WHERE price_change_threshold >= $1 AND direction = 'up'
    ORDER BY created_at DESC
  `;

  try {
    const result = await indexerDbPool.query(query, [threshold]);
    return result.rows as SnipeOrderRow[];
  } catch (error) {
    console.error('Error fetching snipe orders:', error);
    throw error;
  }
}
export async function getSellOrdersWithThreshold(threshold: number): Promise<SnipeOrderRow[]> {
  const query = `
    SELECT *
    FROM snipe_orders
    WHERE price_change_threshold <= $1 AND direction = 'down'
    ORDER BY created_at DESC
  `;

  try {
    const result = await indexerDbPool.query(query, [threshold]);
    return result.rows as SnipeOrderRow[];
  } catch (error) {
    console.error('Error fetching snipe orders:', error);
    throw error;
  }
}

export async function getAllOpenSnipeOrders(): Promise<SnipeOrderRow[]> {
  // target_token_amount is the total amount of the target token that has been swapped
  // it is used for take profits
  const query = `
    SELECT 
      snipe_orders.*,
      COALESCE(SUM(agent_activity.to_amount) FILTER (
        WHERE agent_activity.source = 'SNIPE' AND agent_activity.success = TRUE
      ), 0) AS target_token_amount
    FROM alura.snipe_orders AS snipe_orders
    LEFT JOIN alura.agent_swap_activity AS agent_activity
      ON snipe_orders.id = agent_activity.order_id
    WHERE snipe_orders.closed = FALSE
    GROUP BY snipe_orders.id
    -- ORDER BY snipe_orders.id DESC;

  `;

  try {
    const result = await indexerDbPool.query(query);
    return result.rows as SnipeOrderRow[];
  } catch (error) {
    console.error('Error fetching open snipe orders:', error);
    throw error;
  }
}

export async function setSnipeOrderClosed(id: number, wallet_address: string): Promise<void> {
  const query = `
    UPDATE alura.snipe_orders
    SET closed = TRUE
    WHERE id = $1 AND agent_wallet_address = $2
  `;
  try {
    await indexerDbPool.query(query, [id, wallet_address]);
  } catch (error) {
    console.error('Error setting snipe order as closed:', error);
    throw error;
  }
}

export async function getPercentageChanges(tokens: string[]): Promise<any> {
  const query = `
    WITH asset_rates AS (
      SELECT
        asset_x,
        FIRST_VALUE(rate) OVER (
          PARTITION BY asset_x 
          ORDER BY transaction_timestamp ASC
        ) AS rate_start,
        LAST_VALUE(rate) OVER (
          PARTITION BY asset_x 
          ORDER BY transaction_timestamp ASC 
          ROWS BETWEEN UNBOUNDED PRECEDING AND UNBOUNDED FOLLOWING
        ) AS rate_end
      FROM alura.swap_activity
      WHERE
        asset_x = ANY($1)  -- Array of assets passed as parameter
        AND transaction_timestamp >= NOW() - INTERVAL '1 hour'
    )

    SELECT
      asset_x,
      rate_start,
      rate_end,
      ROUND(((rate_end - rate_start) / rate_start) * 100, 2) AS percentage_change
    FROM (
      SELECT DISTINCT asset_x, rate_start, rate_end
      FROM asset_rates
    ) sub;
  `;

  try {
    const result = await indexerDbPool.query(query, [tokens]);
    return result.rows;
  } catch (error) {
    console.error('Error calculating percentage changes:', error);
    throw error;
  }
}

export async function fetchOpenSnipeRecords(
  wallet_address: string,
  token_address?: string,
): Promise<SnipeOrderRow[]> {
  try {
    let query = `
            SELECT * FROM alura.snipe_orders 
            WHERE agent_wallet_address = $1 AND target_token_address = $2 AND closed = false
        `;
    let values = [wallet_address, token_address];

    if (!token_address) {
      query = `
                SELECT * FROM alura.snipe_orders 
                WHERE agent_wallet_address = $1 AND closed = false
            `;
      values = [wallet_address];
    }
    const { rows } = await indexerDbPool.query(query, values);
    return rows;
  } catch (error) {
    console.error('Error fetching open DCA records:', error);
    throw error;
  }
}

export async function setSnipeOrderExecuted(ids: number[]): Promise<void> {
  if (!ids || ids.length === 0) {
    return;
  }

  const query = `
    UPDATE alura.snipe_orders
    SET 
      executed = TRUE,
      closed = CASE 
        WHEN take_profit_percent IS NULL THEN TRUE 
        ELSE closed
      END
    WHERE id = ANY($1);
  `;

  try {
    await indexerDbPool.query(query, [ids]);
  } catch (error) {
    console.error('Error setting snipe orders as executed:', ids, error);
    throw error;
  }
}

export async function setTPPrices(orders: { orderId: number; tpPrice: number }[]): Promise<void> {
  if (orders.length === 0) return;

  const placeholders = orders.flatMap((_, index) => [`$${index * 2 + 1}`, `$${index * 2 + 2}`]);

  const query = `
    UPDATE alura.snipe_orders
    SET 
      tp_price = CASE id 
        ${orders.map((_, index) => `WHEN $${index * 2 + 1} THEN $${index * 2 + 2}::NUMERIC`).join('\n')}
      END,
      closed = true
    WHERE id IN (${orders.map((_, index) => `$${index * 2 + 1}`).join(', ')})
  `;

  try {
    const values = orders.flatMap(order => [order.orderId, order.tpPrice]);
    await indexerDbPool.query(query, values);
  } catch (error) {
    console.error('Error setting bulk TP prices:', error);
    throw error;
  }
}

export async function getCurrentPriceOfToken(
  asset_x: string,
  asset_y: string = '0x1::aptos_coin::AptosCoin',
): Promise<{ rate: number; asset_y: string } | null> {
  const query = `
    SELECT 
      apt_usd, price,
      CASE 
        WHEN asset_x = $2 THEN 1 / rate
        ELSE rate
      END AS rate
    FROM alura.swap_activity
    WHERE 
      ((asset_x = $1 AND asset_y = $2) OR (asset_y = $1 AND asset_x = $2)) 
      AND apt_usd IS NOT NULL AND rate > 0
    ORDER BY transaction_timestamp DESC
    LIMIT 1;
  `;
  try {
    const result = await indexerDbPool.query(query, [asset_x, asset_y]);
    if (result.rows.length === 0 || !result.rows[0].apt_usd || !result.rows[0].rate) {
      const price = await getAssetUsdPrice(asset_x);
      if (!price) return null;
      return {
        rate: price.usdPrice,
        asset_y,
      };
    }
    if (result.rows[0].price) return { rate: Number(result.rows[0].price), asset_y };
    const decimal_x = getTokenDecimals(asset_x);
    const decimal_y = getTokenDecimals(asset_y);
    if (decimal_x !== null && decimal_y !== null) {
      // rate now returns the price of asset_x in terms of asset_y in USD
      return {
        rate: (result.rows[0].rate * 10 ** -decimal_y * result.rows[0].apt_usd) / 10 ** -decimal_x,
        asset_y,
      };
    }
    return null;
  } catch (error) {
    console.error('Error calculating prices: \n', error);
    throw error;
  }
}

export async function insertSwapActivities(activities: AgentSwapActivity[]): Promise<void> {
  if (!activities.length) return;

  const values: any[] = [];
  const placeholders: string[] = [];

  activities.forEach((activity, index) => {
    const baseIndex = index * 11;
    placeholders.push(
      `($${baseIndex + 1}, $${baseIndex + 2}, $${baseIndex + 3}, $${baseIndex + 4}, $${baseIndex + 5}, $${baseIndex + 6}, $${baseIndex + 7}, $${baseIndex + 8}, $${baseIndex + 9}, $${baseIndex + 10}, $${baseIndex + 11})`,
    );

    values.push(
      activity.sender,
      activity.transaction_timestamp,
      activity.from_token,
      activity.to_token,
      activity.to_token_price_usd,
      activity.from_amount,
      activity.to_amount,
      activity.transaction_hash,
      activity.success,
      activity.order_id,
      activity.source,
    );
  });

  const query = `
    INSERT INTO alura.agent_swap_activity (
      sender, transaction_timestamp, from_token, to_token, to_token_price_usd, from_amount, to_amount,
      transaction_hash, success, order_id, source
    )
    VALUES ${placeholders.join(', ')}
    ON CONFLICT (transaction_hash) DO NOTHING;
  `;

  try {
    await indexerDbPool.query(query, values);
  } catch (error) {
    console.error('Failed to insert swap activities:', error);
    throw error;
  }
}

export async function getSnipeOrderActivityWithId(id: Number) {
  try {
    const query = `
    SELECT *
    FROM alura.agent_swap_activity
    WHERE source IN ('SNIPE', 'SNIPE-TP') AND order_id = $1 AND success = TRUE
    `;

    const result = await indexerDbPool.query(query, [id]);
    return result.rows;
  } catch (error) {
    console.error('Error fetching snipe order activity:\n', error);
    throw error;
  }
}
export async function getSnipeOrderWithId(id: Number, agent_address: string) {
  try {
    const query = `
    SELECT *
    FROM alura.snipe_orders
    WHERE id = $1 AND agent_wallet_address = $2
    `;

    const result = await indexerDbPool.query(query, [id, agent_address]);
    return result.rows;
  } catch (error) {
    console.error('Error fetching snipe order:\n', error);
    throw error;
  }
}
