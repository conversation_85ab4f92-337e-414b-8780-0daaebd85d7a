import coinDBPool from '@/config/coinDB';
import indexerDbPool from '@/config/indexerDb';
import { AgentTokenTransfer } from '../schemas/indexer/agentTokenTransfersQuery';
import format from 'pg-format';

export async function getTokenTransfers(
  wallet_address: string,
  cursor: string,
): Promise<AgentTokenTransfer[]> {
  const getTransferActivitiesQuery = `
    SELECT DISTINCT ON (faa.transaction_version)
        faa.transaction_version,
        faa.event_index,
        faa.amount,
        faa.type,
        faa.transaction_timestamp AS date,
        faa.asset_type,
        faa.is_transaction_success,
        faa.owner_address,
        faa.entry_function_id_str,
        fam.decimals,
        fam.icon_uri,
        fam.symbol,
        fam.name
    FROM fungible_asset_activities faa
    INNER JOIN fungible_asset_metadata fam ON fam.asset_type = faa.asset_type
    WHERE faa.owner_address = $1
      --AND ('0x1::aptos_coin::AptosCoin'::text IS NULL OR faa.asset_type = '0x1::aptos_coin::AptosCoin')
      AND faa.type != '0x1::aptos_coin::GasFeeEvent' AND faa.transaction_timestamp > $2
      -- AND entry_function_id_str = '0x1::aptos_account::transfer_coins' OR entry_function_id_str = 'aptos_account::transfer_fungible_assets'
    ORDER BY faa.transaction_version DESC, faa.event_index DESC
  `;

  const values = [wallet_address, cursor];

  try {
    const { rows } = await coinDBPool.query(getTransferActivitiesQuery, values);
    if (!rows || !rows.length) return [];
    const transferData: AgentTokenTransfer[] = rows
      .filter(
        (item: any) =>
          item.entry_function_id_str === '0x1::aptos_account::transfer_coins' ||
          item.entry_function_id_str === '0x1::aptos_account::transfer_fungible_assets',
      )
      .map((item: any) => ({
        transaction_version: String(item.transaction_version),
        agent_address: String(item.owner_address),
        event: item.type,
        transaction_timestamp: new Date(item.date).toISOString(),
        asset_address: String(item.asset_type),
        amount: (item.amount / 10 ** item.decimals).toString(),
        success: Boolean(item.is_transaction_success),
      }));

    return transferData;
  } catch (err) {
    console.error('Failed to fetch token transfers:', err);
    throw new Error('DB query failed');
  }
}

export async function insertTokenTransfers(data: AgentTokenTransfer[]): Promise<number | null> {
  if (data.length === 0) return 0;

  try {
    const values = data.map(item => [
      item.transaction_version,
      item.agent_address,
      item.event,
      item.transaction_timestamp,
      item.asset_address,
      item.amount,
      item.success,
    ]);

    const query = format(
      `
      INSERT INTO alura.agent_token_transfers 
        (transaction_version, agent_address, event, transaction_timestamp, asset_address, amount, success)
      VALUES %L
      ON CONFLICT (transaction_version) DO NOTHING
      RETURNING 1
    `,
      values,
    );

    const result = await indexerDbPool.query(query);
    return result.rowCount; // or result.rows.length
  } catch (err) {
    console.error('Failed to sync token transfers:', err);
    throw err;
  }
}

export async function getLastTransferActivityTimestampOfAgent(
  agent_address: string,
): Promise<string> {
  const baseQuery = `
    SELECT transaction_timestamp
    FROM alura.agent_token_transfers
    WHERE agent_address = $1
  `;

  try {
    const result = await indexerDbPool.query(baseQuery, [agent_address]);
    return result.rows[0]?.transaction_timestamp || getTwoDaysAgoISO();
  } catch (err) {
    throw new Error('Failed to fetch agent token transfers:');
  }
}

function getTwoDaysAgoISO(): string {
  const now = new Date();
  now.setDate(now.getDate() - 2);
  return now.toISOString();
}

export async function syncAgentTokenTransfers(agent_address: string) {
  try {
    const lastTransferTimestamp = await getLastTransferActivityTimestampOfAgent(agent_address);
    const transferData = await getTokenTransfers(agent_address, lastTransferTimestamp);
    const insertedCount = await insertTokenTransfers(transferData);
    return insertedCount;
  } catch (err) {
    console.error('Failed to sync agent token transfers:');
    throw err;
  }
}

export async function getAgentTransferActivities(agent_address: string) {
  try {
    const query = `
      SELECT transaction_version, agent_address, event, transaction_timestamp as date, asset_address, amount, success
      FROM alura.agent_token_transfers
      WHERE agent_address = $1
      ORDER BY transaction_timestamp DESC
      LIMIT 10
    `;
    const result = await indexerDbPool.query(query, [agent_address]);
    return result.rows;
  } catch (error) {
    throw error;
  }
}
