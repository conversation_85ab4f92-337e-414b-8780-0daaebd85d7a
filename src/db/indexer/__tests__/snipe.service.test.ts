import { getAssetUsdPrice } from '@/utils/blockchain/transaction/coinTxnBuilder';
import { getCurrentPriceOfToken, getTokenDecimals } from '../snipe';
import indexerDbPool from '@/config/indexerDb';

// ---------------- Mocks ----------------
jest.mock('@/config/indexerDb', () => ({
  __esModule: true,
  default: {
    query: jest.fn(),
  },
}));

jest.mock('@/utils/blockchain/transaction/coinTxnBuilder', () => ({
  getAssetUsdPrice: jest.fn(),
}));

jest.mock('../snipe', () => {
  const actual = jest.requireActual('../snipe');
  return {
    ...actual,
    getTokenDecimals: jest.fn(),
  };
});

// ---------------- Test Constants ----------------
const APT = '0x1::aptos_coin::AptosCoin';
const MOCK_ASSET = '0x378d5ba871c3d1bdf477a617f997f23d9e0702de97a02f42925b44fa3abc9866';

// ---------------- Main Test Suite ----------------
describe('snipe service utilities', () => {
  afterEach(() => jest.clearAllMocks());

  describe('getCurrentPriceOfToken', () => {
    beforeEach(() => {
      (getTokenDecimals as jest.Mock).mockImplementation((asset: string) => {
        if (asset === MOCK_ASSET) return 8;
        if (asset === APT) return 8;
        return null;
      });
    });

    it('should return rate from DB when available', async () => {
      const dbRate = 0.0017;
      const aptUsd = 10;

      (indexerDbPool.query as jest.Mock).mockResolvedValueOnce({
        rows: [{ rate: dbRate, apt_usd: aptUsd }],
      });

      const result = await getCurrentPriceOfToken(MOCK_ASSET, APT);
      const expected = {
        rate: (dbRate * 10 ** -8 * aptUsd) / 10 ** -8,
        asset_y: APT,
      };

      expect(result).toEqual(expected);
      expect(indexerDbPool.query).toHaveBeenCalledWith(
        expect.stringContaining('FROM alura.swap_activity'),
        [MOCK_ASSET, APT],
      );
    });

    it('should fall back to on-chain price if DB has no results', async () => {
      const fallbackPrice = 1.01;

      (indexerDbPool.query as jest.Mock).mockResolvedValueOnce({ rows: [] });
      (getAssetUsdPrice as jest.Mock).mockResolvedValueOnce({ usdPrice: fallbackPrice });

      const result = await getCurrentPriceOfToken(MOCK_ASSET, APT);

      expect(result).toEqual({ rate: fallbackPrice, asset_y: APT });
      expect(getAssetUsdPrice).toHaveBeenCalledWith(MOCK_ASSET);
    });

    it('should return null if no DB price and on-chain fails', async () => {
      (indexerDbPool.query as jest.Mock).mockResolvedValueOnce({ rows: [] });
      (getAssetUsdPrice as jest.Mock).mockResolvedValueOnce(null);

      const result = await getCurrentPriceOfToken(MOCK_ASSET, APT);

      expect(result).toEqual(null);
    });
  });

  describe('getTokenDecimals', () => {
    it('returns decimals for known tokens', () => {
      (getTokenDecimals as jest.Mock).mockImplementation((asset: string) => {
        if (asset === '0x1::usdt::USDT') return 6;
        if (asset === '0x1::aptos_coin::AptosCoin') return 8;
        return null;
      });

      expect(getTokenDecimals('0x1::usdt::USDT')).toBe(6);
      expect(getTokenDecimals('0x1::aptos_coin::AptosCoin')).toBe(8);
    });

    it('returns null for unknown tokens', () => {
      (getTokenDecimals as jest.Mock).mockReturnValue(null);
      expect(getTokenDecimals('0xUnknown')).toBe(null);
    });
  });
});
