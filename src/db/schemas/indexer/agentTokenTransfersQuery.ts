export const agentTokenTransfersQuery = `
CREATE TABLE IF NOT EXISTS alura.agent_token_transfers (
  id SERIAL PRIMARY KEY,
  transaction_version VARCHAR(250) UNIQUE,
  agent_address VARCHAR(250),
  event VARCHAR(250),
  transaction_timestamp TIMESTAMP DEFAULT NOW(),
  asset_address VARCHAR(250),
  amount NUMERIC(30, 10),
  success BOOLEAN DEFAULT TRUE
 );
`;

export interface AgentTokenTransfer {
  id?: number;
  transaction_version: string;
  agent_address: string;
  event: string;
  transaction_timestamp: string;
  asset_address: string;
  amount: string;
  success?: boolean;
}
