import { initializeTables } from '@/db/schemas/indexer/init';
import dotenv from 'dotenv';
import { Pool } from 'pg';
const { NODE_ENV } = process.env;

dotenv.config();

const indexerDbPool = new Pool({
  connectionString: process.env.INDEXER_DATABASE_URL,
  ssl: false,
});

(async () => {
  if (NODE_ENV === 'test') {
    return;
  }
  try {
    await initializeTables(indexerDbPool);
  } catch (error) {
    console.error('❌ Failed to create tables:', error);
    throw error;
  }
})();

export default indexerDbPool;
