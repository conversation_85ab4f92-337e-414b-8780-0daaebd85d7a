// src/app.ts
import express from 'express';
import cors from 'cors';
import apiRoutes from './routes/index';
import dotenv from 'dotenv';

import { tryInitializeCronJobs } from './utils/cron-job/cron_scheduler';
import { runKeyMigration } from '@/utils/migrateKeys';

dotenv.config();

const app = express();

// Middleware to handle CORS - Allow all origins
app.use(
  cors({
    origin: '*', // Allow all origins
    methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS'],
    allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With'],
    credentials: false, // Set to false when using origin: '*'
  }),
);

// Middleware to parse incoming requests with JSON payloads
app.use(express.json());

app.use(express.urlencoded({ extended: true }));

// Routes
app.use('/api/', apiRoutes);
// Initialize migration and cron jobs
(async () => {
  // try {
  //   // Run key migration first (one-time operation)
  //   await runKeyMigration();
  // } catch (error) {
  //   console.error('❌ Key migration failed:', error);
  //   // Don't stop the server if migration fails
  // }
  // Initialize cron jobs
  // await tryInitializeCronJobs();
})();
export default app;
