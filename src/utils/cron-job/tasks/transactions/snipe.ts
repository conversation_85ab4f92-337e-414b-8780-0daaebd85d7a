import {
  AgentSwapActivity,
  getAllOpenSnipeOrders,
  getCurrentPriceOfToken,
  getPercentageChanges,
  insertSwapActivities,
  setSnipeOrderClosed,
  setSnipeOrderExecuted,
  setTPPrices,
  SnipeOrderRow,
} from '@/db/indexer/snipe';
import {
  SwapParams,
  swapTokens,
  swapTokensWithPanoraApi,
} from '@/utils/blockchain/transaction/coinTxnBuilder';
import * as agentService from '@/services/agent';
import decryptPrivateKey from '@/utils/blockchain/cryptography/decryptPrivateKey';

type PriceEntry = {
  rate: number;
  asset_x: string;
  asset_y: string;
};

type Order = SnipeOrderRow;

interface Logger {
  log: (msg: string) => void;
  error: (msg: string, error?: unknown) => void;
}

export async function checkExecutableSnipeOrders(logger: Logger = console): Promise<void> {
  try {
    const orders = await getAllOpenSnipeOrders();
    if (!orders.length) {
      return;
    }
    const tokens = Array.from(new Set(orders.map(order => order.target_token_address)));
    const fetchedPrices = await Promise.all(
      tokens.map(async token => {
        try {
          const priceInfo = await getCurrentPriceOfToken(token);
          if (!priceInfo) {
            console.warn(`Failed to fetch price for token ${token}`);
            return null;
          }
          return {
            rate: priceInfo.rate,
            asset_x: token,
            asset_y: priceInfo.asset_y,
          };
        } catch (error) {
          console.warn(`Failed to fetch price for token ${token}\n`, error);
          return null;
        }
      }),
    );
    const prices = fetchedPrices.filter(
      priceDetails => priceDetails !== null && priceDetails.rate > 0,
    ) as PriceEntry[];

    const executableOrders = getExecutableOrders(orders, prices);
    const executableTPs = getExecutableTPs(orders, prices);

    const { executedOrderIds, activities = [] } = await executeOrders(executableOrders, logger);
    const { executedTakeProfits, activities: tpActivities = [] } = await executeTakeProfits(
      executableTPs,
      prices,
    );

    if (executedOrderIds.length > 0) {
      await updateExecutedOrders(executedOrderIds, logger);
    }
    if (activities.length > 0 || tpActivities.length > 0) {
      const combinedActivities = [...activities, ...tpActivities];
      await insertSwapActivities(combinedActivities);
    }
    if (executedTakeProfits.length > 0) {
      await updateTPs(executedTakeProfits);
    }
    return;
  } catch (error: Error | any) {
    console.log(error);
  }
}

function getExecutableOrders(orders: Order[], prices: PriceEntry[]): Order[] {
  return orders.filter(order => {
    return prices.some(price => {
      if (order.executed === true || price.asset_x !== order.target_token_address) {
        return false;
      }
      const change = ((price.rate - order.reference_price) * 100) / order.reference_price;
      const threshold = order.price_change_threshold;
      if (order.direction === 'up') {
        return change >= threshold;
      } else if (order.direction === 'down') {
        return change <= -threshold;
      }
      return false;
    });
  });
}

function getExecutableTPs(orders: Order[], prices: PriceEntry[]): Order[] {
  const executableTPs: Order[] = [];

  const executedOrdersWithTP = orders.filter(
    order =>
      order.executed === true &&
      (order.take_profit_percent !== null || order.take_profit_percent !== undefined) &&
      order.tp_price === null,
  );
  if (!executedOrdersWithTP.length) {
    return [];
  }
  for (const order of executedOrdersWithTP) {
    const price = prices.find(price => price.asset_x === order.target_token_address);
    if (!price) {
      continue;
    }
    const adjustmentFactor =
      order.direction === 'down'
        ? 1 - order.price_change_threshold / 100
        : 1 + order.price_change_threshold / 100;
    const executed_price = order.reference_price * adjustmentFactor;
    // const percentageChange = ((price.rate - order.reference_price) * 100) / order.reference_price;
    const percentageChange = ((price.rate - executed_price) * 100) / executed_price;

    if (percentageChange >= order.take_profit_percent!) {
      executableTPs.push(order);
    }
  }

  return executableTPs;
}

export async function executeOrders(
  orders: Order[],
  logger: Logger,
): Promise<{ executedOrderIds: number[]; activities: AgentSwapActivity[] }> {
  const executedOrderIds: number[] = [];
  const activities: AgentSwapActivity[] = [];

  for (const order of orders) {
    let success = false;
    let to_amount = 0;
    let transaction_hash: string | null = null;
    let executed_price: number = 0;
    let transaction_timestamp: string = new Date().toISOString();

    try {
      const executed = await executeSingleOrder(order);
      if (executed) {
        success = executed.success;
        transaction_hash = executed.hash;
        to_amount = executed.to_amount;
        executed_price = executed.to_token_price_usd;
        transaction_timestamp = executed.transaction_timestamp;
        executedOrderIds.push(Number(order.id));
      }
    } catch (error) {
      logger.error(`❌ Failed to execute order ${order.id}\n`, error);
    }
    activities.push({
      order_id: order.id,
      sender: order.agent_wallet_address,
      from_token: order.base_token_address,
      to_token: order.target_token_address,
      to_token_price_usd: executed_price,
      from_amount: order.buy_amount,
      to_amount,
      transaction_timestamp: transaction_timestamp,
      source: 'SNIPE',
      success,
      transaction_hash,
    });
  }

  return { executedOrderIds, activities };
}

async function executeTakeProfits(
  orders: Order[],
  prices: PriceEntry[],
): Promise<{
  executedTakeProfits: { orderId: number; tpPrice: number }[];
  activities: AgentSwapActivity[];
}> {
  const takeProfits: { orderId: number; tpPrice: number }[] = [];
  const activities: AgentSwapActivity[] = [];

  for (const order of orders) {
    const price = prices.find(price => price.asset_x === order.target_token_address);
    if (!price || !order.take_profit_percent) {
      continue;
    }
    // const threshold_price = order.reference_price * (1 + order.take_profit_percent / 100);
    // const tokens_at_threshold = order.buy_amount / threshold_price;

    const executableTPOrder = {
      ...order,
      base_token_address: order.target_token_address,
      target_token_address: order.base_token_address,
      // consider slippage
      buy_amount: parseFloat((order.target_token_amount * 0.99).toFixed(7)),
    };
    const executed = await executeSingleOrder(executableTPOrder);
    if (executed?.success) {
      takeProfits.push({ orderId: Number(order.id), tpPrice: price.rate });
      activities.push({
        order_id: order.id,
        sender: order.agent_wallet_address,
        from_token: executed.from_token,
        to_token: executed.to_token,
        to_token_price_usd: executed.to_token_price_usd,
        from_amount: executed.from_amount,
        to_amount: executed.to_amount,
        transaction_timestamp: executed.transaction_timestamp,
        source: 'SNIPE-TP',
        success: executed.success,
        transaction_hash: executed.hash,
      });
    }
  }
  return { executedTakeProfits: takeProfits, activities };
}

interface ExecutedOrder {
  hash: string;
  success: boolean;
  order_id: number;
  from_token: string;
  to_token: string;
  to_token_price_usd: number;
  from_amount: number;
  to_amount: number;
  transaction_timestamp: string;
}
async function executeSingleOrder(order: SnipeOrderRow): Promise<ExecutedOrder | null> {
  try {
    const {
      id,
      target_token_address,
      base_token_address,
      buy_amount,
      agent_wallet_address,
      slippage_tolerance,
    } = order;

    const swapParams: SwapParams = {
      fromTokenAddress: base_token_address as `0x${string}`,
      toTokenAddress: target_token_address as `0x${string}`,
      fromTokenAmount: buy_amount.toString() as `0x${string}`,
      toWalletAddress: agent_wallet_address as `0x${string}`,
      slippagePercentage: slippage_tolerance?.toString(),
    };

    const agent = await agentService.getAgentByPublicKey(agent_wallet_address);
    if (!agent) {
      throw new Error(`Agent not found for order ${id}`);
    }

    const privateKey = agent.privateKey;
    if (!privateKey) {
      throw new Error(`Agent wallet not found for ${agent_wallet_address}`);
    }

    const decryptedPrivateKey = decryptPrivateKey(privateKey);
    // const result = await swapTokens(swapParams, decryptedPrivateKey);
    const result = await swapTokensWithPanoraApi(swapParams, decryptedPrivateKey);

    if (!result.success || !result.hash) {
      throw new Error(`Swap failed for order ${id}`);
    }

    return {
      hash: result.hash,
      success: result.success,
      order_id: id,
      from_token: base_token_address,
      to_token: target_token_address,
      to_token_price_usd: result.price,
      from_amount: buy_amount,
      to_amount: result.toTokenAmount,
      transaction_timestamp: result.timestamp,
    };
  } catch (error: Error | any) {
    const messages_to_close_order = ['EINSUFFICIENT_BALANCE', 'Request failed with status 400'];
    const should_close_order = messages_to_close_order.some(message =>
      error.message.includes(message),
    );
    if (should_close_order) {
      console.info(`closing snipe order ${order.id} due to:\n ${error.message}`);
      await setSnipeOrderClosed(order.id, order.agent_wallet_address);
    }
    return null;
  }
}

async function updateExecutedOrders(orderIds: number[], logger: Logger): Promise<void> {
  if (!orderIds.length) {
    return;
  }

  try {
    await setSnipeOrderExecuted(orderIds);
  } catch (error) {
    logger.error('Failed to mark orders as executed', error);
    logger.error('Failed Ids: \n', orderIds);
    throw error; // Rethrow to allow caller to handle
  }
}

async function updateTPs(takeProfits: { orderId: number; tpPrice: number }[]): Promise<void> {
  try {
    await setTPPrices(takeProfits);
  } catch (error) {
    throw error;
  }
}
