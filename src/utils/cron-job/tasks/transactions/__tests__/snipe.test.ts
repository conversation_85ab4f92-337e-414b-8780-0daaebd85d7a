// ----------------------- Mocks (must be before imports) -----------------------
jest.mock('@/db/indexer/snipe');
jest.mock('@/utils/blockchain/transaction/coinTxnBuilder');
jest.mock('@/services/agent');
jest.mock('@/utils/blockchain/cryptography/decryptPrivateKey');

// ----------------------- Imports -----------------------
import * as db from '@/db/indexer/snipe';
import * as blockchain from '@/utils/blockchain/transaction/coinTxnBuilder';
import * as agentService from '@/services/agent';
import decryptPrivateKey from '@/utils/blockchain/cryptography/decryptPrivateKey';

import { checkExecutableSnipeOrders } from '../snipe';

// ----------------------- Utilities -----------------------

const createMockLogger = () => ({
  log: jest.fn(),
  error: jest.fn(),
});

const createMockOrder = (overrides = {}) => ({
  id: 1,
  user_id: 'user1',
  target_token_address: 'token1',
  base_token_address: 'token2',
  percentage_change: 10,
  reference_price: 100,
  price_change_threshold: 10,
  direction: 'down',
  executed: false,
  agent_wallet_address: 'wallet-1',
  buy_amount: 100,
  slippage_tolerance: 0.5,
  take_profit_percent: null,
  tp_price: null,
  created_at: new Date(),
  is_open: true,
  target_token_amount: 50,
  ...overrides,
});

const mockSuccessfulSwap = ({
  hash = 'mock-hash',
  toTokenAmount = 95,
  fromTokenAmount = 100,
  timestamp = new Date().toISOString(),
  from_token = 'tokenA',
  to_token = 'tokenB',
} = {}) => ({
  success: true,
  hash,
  toTokenAmount,
  fromTokenAmount,
  from_token,
  to_token,
  transaction_timestamp: timestamp,
});

// ----------------------- Test Suite -----------------------

describe('checkExecutableSnipeOrders', () => {
  let logger: ReturnType<typeof createMockLogger>;

  beforeEach(() => {
    logger = createMockLogger();
    jest.clearAllMocks();

    (agentService.getAgentByPublicKey as jest.Mock).mockResolvedValue({
      privateKey: 'encrypted-key',
    });
    (decryptPrivateKey as jest.Mock).mockReturnValue('decrypted-key');
  });

  it('skips orders when token price is null', async () => {
    const orders = [createMockOrder()];
    (db.getAllOpenSnipeOrders as jest.Mock).mockResolvedValue(orders);
    (db.getCurrentPriceOfToken as jest.Mock).mockResolvedValue(null);

    await checkExecutableSnipeOrders(logger);

    expect(db.setSnipeOrderExecuted).not.toHaveBeenCalled();
    expect(db.insertSwapActivities).not.toHaveBeenCalled();
    expect(logger.error).not.toHaveBeenCalled();
  });

  it('executes only orders meeting threshold conditions', async () => {
    const orders = [
      createMockOrder({ id: 1, price_change_threshold: 10 }), // exact match
      createMockOrder({ id: 2, price_change_threshold: 10.0001 }), // too strict
      createMockOrder({ id: 3, price_change_threshold: 9.999 }), // loose threshold
    ];
    (db.getAllOpenSnipeOrders as jest.Mock).mockResolvedValue(orders);
    (db.getCurrentPriceOfToken as jest.Mock).mockResolvedValue({
      rate: 90,
      asset_y: 'aptos::usdc',
    });

    (blockchain.swapTokensWithPanoraApi as jest.Mock).mockResolvedValue(mockSuccessfulSwap());

    await checkExecutableSnipeOrders(logger);

    expect(db.setSnipeOrderExecuted).toHaveBeenCalledWith([1, 3]);
    expect(blockchain.swapTokensWithPanoraApi).toHaveBeenCalledTimes(2);

    const [activities] = (db.insertSwapActivities as jest.Mock).mock.calls[0];
    expect(activities.map((a: any) => a.order_id).sort()).toEqual([1, 3]);
  });

  it('inserts correct swap activity logs after execution', async () => {
    const orders = [
      createMockOrder({
        id: 101,
        base_token_address: 'tokenA',
        target_token_address: 'tokenB',
        buy_amount: 500,
        agent_wallet_address: 'wallet-101',
      }),
    ];

    (db.getAllOpenSnipeOrders as jest.Mock).mockResolvedValue(orders);
    (db.getCurrentPriceOfToken as jest.Mock).mockResolvedValue({
      rate: 90,
      asset_y: 'aptos::usdc',
    });

    const swapResult = mockSuccessfulSwap({
      hash: 'mock-hash-xyz',
      toTokenAmount: 480,
      fromTokenAmount: 500,
      from_token: 'tokenA',
      to_token: 'tokenB',
      timestamp: '2025-05-25T12:00:00.000Z',
    });

    (blockchain.swapTokensWithPanoraApi as jest.Mock).mockResolvedValue(swapResult);

    await checkExecutableSnipeOrders(logger);

    const [activities] = (db.insertSwapActivities as jest.Mock).mock.calls[0];
    const activity = activities[0];

    expect(activity).toMatchObject({
      order_id: 101,
      sender: 'wallet-101',
      from_token: 'tokenA',
      to_token: 'tokenB',
      from_amount: 500,
      to_amount: 480,
      transaction_hash: 'mock-hash-xyz',
      source: 'SNIPE',
      success: true,
    });

    expect(activity.transaction_timestamp).toEqual(expect.any(Date));
  });

  it('updates take profit orders when triggered', async () => {
    const tpOrder = createMockOrder({
      id: 301,
      executed: true,
      price_change_threshold: 10,
      reference_price: 100,
      take_profit_percent: 5,
      tp_price: null,
      direction: 'down',
      base_token_address: 'tokenY',
      target_token_address: 'tokenX',
      agent_wallet_address: 'wallet-301',
      buy_amount: 100,
      target_token_amount: 50,
    });

    (db.getAllOpenSnipeOrders as jest.Mock).mockResolvedValue([tpOrder]);
    (db.getCurrentPriceOfToken as jest.Mock).mockResolvedValue({
      rate: 94.5,
      asset_y: 'aptos::usdc',
    });

    const tpSwap = mockSuccessfulSwap({
      hash: 'tp-hash-xyz',
      from_token: 'tokenX',
      to_token: 'tokenY',
      fromTokenAmount: 49.5,
      toTokenAmount: 49,
    });

    (blockchain.swapTokensWithPanoraApi as jest.Mock).mockResolvedValue(tpSwap);

    await checkExecutableSnipeOrders(logger);

    expect(db.setTPPrices).toHaveBeenCalledWith([{ orderId: 301, tpPrice: 94.5 }]);

    const [activities] = (db.insertSwapActivities as jest.Mock).mock.calls[0];
    const activity = activities.find((a: any) => a.order_id === 301);

    expect(activity).toMatchObject({
      order_id: 301,
      from_token: 'tokenX',
      to_token: 'tokenY',
      from_amount: 49.5,
      to_amount: 49,
      success: true,
      source: 'SNIPE-TP',
      transaction_hash: 'tp-hash-xyz',
    });

    expect(logger.error).not.toHaveBeenCalled();
  });
});
