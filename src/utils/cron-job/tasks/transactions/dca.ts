import {
  getDCARecordsToOpen,
  setDCARecordClosed,
  setDCARecordUpdatedToNow,
} from '@/db/indexer/dca';
import * as agentService from '@/services/agent';
import decryptPrivateKey from '@/utils/blockchain/cryptography/decryptPrivateKey';
import { getMinutesFromFrequency, sleep } from './utils';
import {
  SwapParams,
  swapTokens,
  swapTokensTest,
  swapTokensWithPanoraApi,
} from '@/utils/blockchain/transaction/coinTxnBuilder';
import { getPriceDetails } from '@/utils/blockchain/indexer/getCoinData';
import { insertSwapActivities } from '@/db/indexer/snipe';

const { NETWORK } = process.env;
if (!NETWORK) {
  throw new Error('Network not set');
}
export async function openDCATrades() {
  try {
    const ordersToOpen = await getDCARecordsToOpen();

    if (!ordersToOpen.length) {
      return;
    }

    for (const order of ordersToOpen) {
      try {
        const {
          id,
          frequency,
          from_token,
          to_token,
          amount,
          toWalletAddress,
          agent_address,
          updated_at,
        } = order;

        const agent = await agentService.getAgentByPublicKey(agent_address);
        if (!agent) {
          throw new Error('Agent not found');
        }
        const privateKey = agent.privateKey;
        if (!privateKey) {
          throw new Error(`Agent wallet not found for ${agent_address}`);
        }
        const decryptedPrivateKey = decryptPrivateKey(privateKey);
        const currentTime = new Date();
        const diffMs = currentTime.getTime() - updated_at.getTime();
        const diffSeconds = Math.floor(diffMs / 1000);
        const minutes = Math.ceil(diffSeconds / 60);
        let swapRes;
        if (minutes >= getMinutesFromFrequency(frequency)) {
          const swapParams: SwapParams = {
            fromTokenAddress: from_token,
            toTokenAddress: to_token,
            fromTokenAmount: amount,
            toWalletAddress: agent_address,
            slippagePercentage: '1',
          };
          if (NETWORK === 'mainnet') {
            // swapRes = await swapTokens(swapParams, decryptedPrivateKey);
            swapRes = await swapTokensWithPanoraApi(swapParams, decryptedPrivateKey);
          }
        }
        if (swapRes?.success) {
          await setDCARecordUpdatedToNow(from_token, frequency, agent_address);
          await insertSwapActivities([
            {
              sender: swapRes.sender,
              transaction_hash: swapRes.hash,
              success: swapRes.success,
              from_token,
              to_token,
              to_token_price_usd: swapRes.price || 0,
              from_amount: Number(amount),
              to_amount: swapRes.toTokenAmount,
              transaction_timestamp: swapRes.timestamp,
              order_id: id,
              source: 'DCA',
            },
          ]);
        }
      } catch (error: Error | any) {
        console.log(`error in dca for order: ${order.id}: \n`, error.message || error);
        if (error.message.includes('EINSUFFICIENT_BALANCE')) {
          console.info(`closing dca order ${order.id} due to insufficient balance.`, order);
          await setDCARecordClosed(order.id, order.agent_address);
        }
      }
    }
  } catch (error) {
    console.log(`error in dca :: \n`, error);
  }
}
