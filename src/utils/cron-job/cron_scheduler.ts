import { initializeCronJobs } from '.';
import indexerDbPool from '@/config/indexerDb';

const LOCK_KEY = 42000;
/**
 * Tries to acquire a distributed advisory lock and run the cron scheduler
 */
export async function tryInitializeCronJobs(): Promise<void> {
  const client = await indexerDbPool.connect();
  try {
    const { rows } = await client.query('SELECT pg_try_advisory_lock($1) AS acquired', [LOCK_KEY]);
    const acquired = rows[0]?.acquired;

    if (!acquired) {
      console.log(`[CRON_INIT] Skipped: another instance holds the lock`);
      client.release();
      return;
    }

    console.log(`[CRON_INIT] Lock acquired, initializing cron jobs`);
    initializeCronJobs();
  } catch (err) {
    client.release();
    console.error('[CRON_INIT] Error acquiring advisory lock:', err);
  }
}
