import axios, { AxiosError } from 'axios';

interface TokenBalanceResponse {
  amount: string;
  metadata?: {
    asset_type: string;
    decimals: number;
    icon_uri: string;
    symbol: string;
  };
}

interface GetTokenBalanceParams {
  walletAddress: string;
  coinType?: string;
  coinAddress?: string;
  signal?: AbortSignal;
}

// Helper function to pad address
const getPaddedAddress = (address: string): string => {
  // Remove 0x prefix if present
  let cleanAddress = address.startsWith('0x') ? address.slice(2) : address;
  
  // Pad to 64 characters (32 bytes) with leading zeros
  cleanAddress = cleanAddress.padStart(64, '0');
  
  // Add 0x prefix back
  return `0x${cleanAddress}`;
};

const getUserCoinBalance = async ({
  walletAddress,
  coinType = "0x1::aptos_coin::AptosCoin",
  coinAddress,
  signal,
}: GetTokenBalanceParams): Promise<string> => {
  try {
    const GRAPHQL_URL = `${process.env.EXPLORER_API}/v1/graphql`;
    let paddedCoinType = coinType;

    if (!paddedCoinType.includes('::')) {
      paddedCoinType = getPaddedAddress(coinType);
    }

    const query = {
      operationName: 'GetFungibleAssetsHeldByUser',
      query: `
        query GetFungibleAssetsHeldByUser($OWNER_ADDRESS: String, $ASSET_TYPE: String) {
          current_fungible_asset_balances(
            where: {
              owner_address: { _eq: $OWNER_ADDRESS },
              asset_type: { _eq: $ASSET_TYPE }
            }
          ) {
            amount
            metadata {
              asset_type
              decimals
              icon_uri
              symbol
            }
          }
        }
      `,
      variables: {
        OWNER_ADDRESS: walletAddress,
        ASSET_TYPE: coinAddress || paddedCoinType,
      },
    };

    const response = await axios.post(GRAPHQL_URL, query, {
      signal,
      headers: {
        'Content-Type': 'application/json',
      },
    });

    const data = response.data.data.current_fungible_asset_balances as TokenBalanceResponse[];
    
    if (!data || data.length === 0) {
      return '0.0000';
    }

    const balance = data[0];
    const formattedAmount = balance.metadata?.decimals 
      ? (parseInt(balance.amount) / Math.pow(10, balance.metadata.decimals)).toFixed(4)
      : balance.amount;

    return formattedAmount;

  } catch (error) {
    console.error('Error fetching token balance:', error instanceof Error ? error.message : 'Unknown error');
    
    if (error instanceof AxiosError && error.response) {
      console.error('Response data:', error.response.data);
      console.error('Response status:', error.response.status);
    }
    
    return '0.0000';
  }
};

export default getUserCoinBalance;