import * as dotenv from 'dotenv';
import { AIAgentMessage } from '@/types/common';
import chatPrompt from './prompts/chat';
import classifyTextPrompt from './prompts/classifyText';
import classifyTextIntentPrompt from './prompts/classifyTextIntent';
import { classifyCryptoAction } from './common';
import { classifyText } from './prompts/classifyInvestPrompt/index';

dotenv.config();

type Intent = 
  | 'profit_invest'
  | 'profit_only'
  | 'invest_only'
  | 'crypto_intent'
  | 'chit_chat'
  | 'other_intent'
  | 'knowledge_base'
  | 'create_tweet';

export abstract class BaseLLMClient {
  modelName: string | null = null;

  constructor() {
    this.setModelName(this.getModelName());
  }

  public setModelName(modelName: string): void {
    if (!modelName) {
      throw new Error('Model name is required');
    }
    this.modelName = modelName;
  }

  protected getModelName(): string {
    if (!this.modelName) {
      throw new Error('Model name has not been set');
    }
    return this.modelName;
  }

  protected abstract makeApiCall(payload: any): Promise<string | void>;

  public async chatWithAgent({
    userMessage,
    agentName,
    agentDescription,
    knowledgeBase = '',
    messages = [],
    maxTokens = 1000000,
    isFirstMessage = false,
  }: {
    userMessage: string;
    agentName: string;
    agentDescription: string;
    knowledgeBase: string;
    messages: AIAgentMessage[];
    maxTokens?: number;
    isFirstMessage?: boolean;
  }): Promise<string | void> {
    let content;
    if (isFirstMessage) {
      content = `When a user greets you with casual phrases like "hey", "hi", "hello", or similar, respond with a short, friendly greeting that mirrors their tone and style. Avoid any formal language, follow-up questions, or offers to help (e.g., no phrases like "How can I assist?"). Keep it light, natural, and conversational—like a friend casually saying hello.`;
    } else {
      content = chatPrompt(agentName, agentDescription, knowledgeBase);
    }

    const payload = {
      model: this.modelName,
      messages: [
        {
          role: 'system',
          content,
        },
        ...messages,
        { role: 'user', content: userMessage },
      ],
      max_tokens: maxTokens, // Shorten response
    };

    // Make the API call
    return this.makeApiCall(payload);
  }

  public async testChatWithAgent(userMessage: string): Promise<void | string> {
    const payload = {
      model: this.modelName,
      messages: [{ role: 'user', content: userMessage }],
    };

    // Make the API call
    return await this.makeApiCall(payload);
  }

  public async classifyText(
    text: string,
  ): Promise<Intent> {

    const intent = classifyText(text);
    if(['profit_invest', 'profit_only', 'invest_only'].includes(intent)) {
      return intent as Intent;
    }

    const prompt = classifyTextPrompt();

    const payload = {
      model: this.modelName,
      messages: [
        { role: 'system', content: prompt },
        { role: 'user', content: text },
      ],
    };

    // Make the API call
    const response = await this.makeApiCall(payload);
    const updatedResponse = response?.toLowerCase()?.trim();

    // Validate and ensure the response matches the expected types
    if (
      typeof updatedResponse === 'string' &&
      ['crypto_intent', 'chit_chat', 'other_intent', 'knowledge_base'].includes(
        updatedResponse,
      )
    ) {
      return updatedResponse as Intent
    } else if (updatedResponse === undefined || updatedResponse === null) {
      return 'chit_chat';
    } else {
      throw new Error('Unexpected response from API');
    }
  }

  public async classifyTextIntent(text: string): Promise<any> {
    const promptText = classifyTextIntentPrompt();

    const payload = {
      model: this.modelName,
      messages: [
        { role: 'system', content: promptText },
        { role: 'user', content: text },
      ],
    };

    // Make the API call
    const response = await this.makeApiCall(payload);

    const updatedResponse = response?.toLowerCase()?.trim();
    if (!updatedResponse) {
      return;
    }
    return classifyCryptoAction(updatedResponse);
  }

  public async getConversationTitleFromMessage(message: string): Promise<string | void> {
    const prompt = `You are a helpful assistant. You are given a message and you need to generate a title for the conversation. The title should be short and descriptive. The title should be less than 10 words. `;
    const payload = {
      model: this.modelName,
      messages: [
        { role: 'system', content: prompt },
        { role: 'user', content: message },
      ],
    };
    // Make the API call
    return await this.makeApiCall(payload);
  }
  
}
export default BaseLLMClient;
