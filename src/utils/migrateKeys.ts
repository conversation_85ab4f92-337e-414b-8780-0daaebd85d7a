#!/usr/bin/env node

/**
 * One-time Private Key Migration Script
 * 
 * This script migrates all agent private keys from the old encryption key 
 * to the new encryption key. Run this once and then delete the file.
 * 
 * Usage: npx ts-node src/utils/migrateKeys.ts
 * 
 * Environment Variables Required:
 * - ENCRYPTION_KEY: The old encryption key
 * - ENCRYPTION_KEY_UPDATED: The new encryption key
 */

import dotenv from 'dotenv';
import { createCipheriv, createDecipheriv, randomBytes } from 'crypto';
import db from '@/config/db';
import { agents } from '@/db/schemas';
import { eq, sql } from 'drizzle-orm';
import { initBlockchain } from '@/utils/trade/blockchain';

// Load environment variables
dotenv.config();

// Track if migration has been executed to prevent multiple runs
let migrationExecuted = false;

// Decrypt private key using OLD encryption key
function decryptPrivateKeyOld(encryptedPrivateKey: string): string {
  const [ivHex, encryptedData] = encryptedPrivateKey.split(':');
  const ENCRYPTION_KEY = process.env.ENCRYPTION_KEY!;
  const iv = Buffer.from(ivHex, 'hex');
  const decipher = createDecipheriv('aes-256-cbc', Buffer.from(ENCRYPTION_KEY, 'hex'), iv);
  let decrypted = decipher.update(encryptedData, 'hex', 'utf8');
  decrypted += decipher.final('utf8');
  return decrypted;
}

// Encrypt private key using NEW encryption key
function encryptPrivateKeyNew(privateKey: string): string {
  if (!privateKey) {
    throw new Error('Private key is undefined or null');
  }

  const ENCRYPTION_KEY_UPDATED = process.env.ENCRYPTION_KEY_UPDATED!;
  if (!ENCRYPTION_KEY_UPDATED) {
    throw new Error('ENCRYPTION_KEY_UPDATED environment variable is not set');
  }

  const IV_LENGTH = 16;
  const iv = randomBytes(IV_LENGTH);
  const cipher = createCipheriv('aes-256-cbc', Buffer.from(ENCRYPTION_KEY_UPDATED, 'hex'), iv);
  let encrypted = cipher.update(privateKey, 'utf8', 'hex');
  encrypted += cipher.final('hex');
  return `${iv.toString('hex')}:${encrypted}`;
}

// Decrypt private key using NEW encryption key (for verification)
function decryptPrivateKeyNew(encryptedPrivateKey: string): string {
  const [ivHex, encryptedData] = encryptedPrivateKey.split(':');
  const ENCRYPTION_KEY_UPDATED = process.env.ENCRYPTION_KEY_UPDATED!;
  
  if (!ENCRYPTION_KEY_UPDATED) {
    throw new Error('ENCRYPTION_KEY_UPDATED environment variable is not set');
  }
  
  const iv = Buffer.from(ivHex, 'hex');
  const decipher = createDecipheriv('aes-256-cbc', Buffer.from(ENCRYPTION_KEY_UPDATED, 'hex'), iv);
  let decrypted = decipher.update(encryptedData, 'hex', 'utf8');
  decrypted += decipher.final('utf8');
  return decrypted;
}

// Get all agents with private keys
async function getAllAgentsWithPrivateKeys() {
  const agentsList = await db
    .select({
      id: agents.id,
      privateKey: agents.privateKey,
      publicKey: agents.publicKey
    })
    .from(agents)
    .where(sql`${agents.privateKey} IS NOT NULL AND ${agents.privateKey} != ''`);
  return agentsList;
}

// Update agent's private key in database
async function updateAgentPrivateKey(agentId: number, newEncryptedPrivateKey: string): Promise<void> {
  await db
    .update(agents)
    .set({ 
      privateKey: newEncryptedPrivateKey,
      updatedAt: new Date().toISOString()
    })
    .where(eq(agents.id, agentId));
}

// Main migration function for app startup (silent mode)
export async function runKeyMigration(): Promise<void> {
  // Check if migration has already been executed in this server instance
  if (migrationExecuted) {
    console.log('[KEY_MIGRATION] Migration already executed in this server instance, skipping...');
    return;
  }

  // Check if both encryption keys are available
  const oldKey = process.env.ENCRYPTION_KEY;
  const newKey = process.env.ENCRYPTION_KEY_UPDATED;

  if (!oldKey || !newKey) {
    console.log('[KEY_MIGRATION] Skipping migration: ENCRYPTION_KEY or ENCRYPTION_KEY_UPDATED not set');
    return;
  }

  if (oldKey === newKey) {
    console.log('[KEY_MIGRATION] Skipping migration: Old and new encryption keys are the same');
    migrationExecuted = true;
    return;
  }

  try {
    console.log('[KEY_MIGRATION] Starting private key migration...');
    await migrateKeys();
    migrationExecuted = true;
  } catch (error) {
    console.error('[KEY_MIGRATION] Migration failed:', error);
    // Don't throw error to prevent app startup failure
  }
}

async function migrateKeys(): Promise<void> {
  console.log('🔄 Starting Private Key Migration...');
  
  // Check if both encryption keys are available
  const oldKey = process.env.ENCRYPTION_KEY;
  const newKey = process.env.ENCRYPTION_KEY_UPDATED;

  if (!oldKey || !newKey) {
    throw new Error('Missing environment variables: ENCRYPTION_KEY or ENCRYPTION_KEY_UPDATED not set');
  }

  if (oldKey === newKey) {
    console.log('⚠️  Old and new encryption keys are the same, no migration needed');
    return;
  }

  try {
    console.log('📋 Fetching agents with private keys...');

    // Get all agents with private keys
    const allAgents = await getAllAgentsWithPrivateKeys();

    if (allAgents.length === 0) {
      console.log('✅ No agents found with private keys, migration complete');
      return;
    }

    console.log(`📊 Found ${allAgents.length} agents to migrate`);

    let successCount = 0;
    let errorCount = 0;

    for (const agent of allAgents) {
      if (!agent.privateKey) {
        console.log(`⏭️  Skipping agent ${agent.id}: No private key found`);
        continue;
      }

      try {
        console.log(`🔄 Processing agent ${agent.id}...`);
        
        // Decrypt private key using old encryption key
        const decryptedPrivateKey = decryptPrivateKeyOld(agent.privateKey);
        
        // Verify the decrypted key works by initializing blockchain
        const { account } = await initBlockchain(decryptedPrivateKey);
        
        // Verify the account address matches the stored public key (if available)
        if (agent.publicKey && account.accountAddress.toString() !== agent.publicKey) {
          console.warn(`⚠️  Agent ${agent.id} public key mismatch. Stored: ${agent.publicKey}, Derived: ${account.accountAddress.toString()}`);
        }

        // Encrypt with new key
        const newEncryptedPrivateKey = encryptPrivateKeyNew(decryptedPrivateKey);

        // Verify the new encryption works by testing decryption
        try {
          const testDecrypted = decryptPrivateKeyNew(newEncryptedPrivateKey);
          if (testDecrypted !== decryptedPrivateKey) {
            throw new Error('New encryption verification failed - decrypted key does not match original');
          }
        } catch (verifyError) {
          console.error(`❌ Verification failed for agent ${agent.id}:`, verifyError);
          errorCount++;
          continue;
        }

        // Update in database
        await updateAgentPrivateKey(agent.id, newEncryptedPrivateKey);

        console.log(`✅ Successfully migrated agent ${agent.id}`);
        successCount++;

      } catch (error) {
        console.error(`❌ Failed to migrate agent ${agent.id}:`, error);
        errorCount++;
      }
    }

    console.log('\n📊 Migration Summary:');
    console.log(`✅ Successful migrations: ${successCount}`);
    console.log(`❌ Failed migrations: ${errorCount}`);
    console.log(`📋 Total agents processed: ${allAgents.length}`);

    if (errorCount === 0) {
      console.log('\n🎉 Migration completed successfully!');
      console.log('💡 You can now delete this script file.');
      console.log('💡 Remember to update your application to use ENCRYPTION_KEY_UPDATED for new operations.');
    } else {
      console.log('\n⚠️  Migration completed with some errors. Please review the failed agents.');
      throw new Error(`Migration completed with ${errorCount} errors`);
    }

  } catch (error) {
    console.error('💥 Migration failed:', error);
    throw error;
  }
}

// Run the migration if this file is executed directly
if (require.main === module) {
  migrateKeys()
    .then(() => {
      console.log('🏁 Migration script finished');
      process.exit(0);
    })
    .catch((error) => {
      console.error('💥 Migration script failed:', error);
      process.exit(1);
    });
}
