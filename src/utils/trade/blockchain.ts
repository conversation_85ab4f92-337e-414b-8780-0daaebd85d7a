import { MerkleClient, MerkleClientConfig } from "@merkletrade/ts-sdk";
import { Network, AptosConfig } from "@aptos-labs/ts-sdk";

import {
  Account,
  Aptos,
  Ed25519Private<PERSON>ey,
  PrivateKey,
  PrivateKeyVariants,
} from "@aptos-labs/ts-sdk";
import dotenv from "dotenv";
import fetch from "node-fetch";
import * as tradeService from '@/services/trade';
import { fetchOHLCVFromBinance } from '@/utils/trade/binanceHistorical';
import { evaluateSignalOnly } from '@/utils/trade/strategyEngine';
import { checkSignals, buildIndicatorInputs } from '@/utils/trade/signals';
import { openMarketOrder } from "../blockchain/transaction/tradeTransactions";

dotenv.config();


export const priceFeeds: Record<string,string> = {
  BTC_USD: "0xe62df6c8b4a85fe1a67db44dc12de5db330f7ac66b72dc658afedf0f4a415b43",
  ETH_USD: "0xff61491a931112ddf1bd8147cd1b641375f79f5825126d665480874634fd0ace",
  ADA_USD: "0x2a01deaec9e51a579277b34b122399984d0bbf57e2458a7e42fecd2829867a0d",
  XRP_USD: "0xec5d399846a9209f3fe5881d70aae9268c94339ff9817e8d18ff19fa05eea1c8",
  NEIRO_USD: "0x83dfac550d6066d5c837e99c27e38a392c5cccc9a637044c2ef6c843336da444",
  DOGS_USD: "0x3b75dbeeb6310f6264c812dd8411a6b30dc6d02cec4bca2447c88335127564f4",
  PNUT_USD: "0x116da895807f81f6b5c5f01b109376e7f6834dc8b51365ab7cdfa66634340e54",
  FLOKI_USD: "0x6b1381ce7e874dc5410b197ac8348162c0dd6c0d4c9cd6322672d6c2b1d58293",
  BOME_USD: "0x30e4780570973e438fdb3f1b7ad22618b2fc7333b65c7853a7ca144c39052f7a",
  LTC_USD: "0x6e3f3fa8253588df9326580180233eb791e03b443a3ba7a1d892e73874e19a54",
  DOGE_USD: "0xdcef50dd0a4cd2dcc17e45df1676dcb336a11a61c69df7a0299b0150c672d25c",
  EIGEN_USD: "0xc65db025687356496e8653d0d6608eec64ce2d96e2e28c530e574f0e4f712380",
  TAO_USD: "0x410f41de235f2db824e562ea7ab2d3d3d4ff048316c61d629c0b93f58584e1af",
  ZRO_USD: "0x3bd860bea28bf982fa06bcf358118064bb114086cc03993bd76197eaab0b8018",
  OP_USD: "0x385f64d993f7b77d8182ed5003d97c60aa3361f3cecfe711544d2d59165e9bdf",
  SHIB_USD: "0xf0d57deca57b3da2fe63a493f4c25925fdfd8edf834b20f93e1f84dbd1504d4a",
  BONK_USD: "0x72b021217ca3fe68922a19aaf990109cb9d84e9ad004b4d2025ad6f529314419",
  HBAR_USD: "0x3728e591097635310e6341af53db8b7ee42da9b3a8d918f9463ce9cca886dfbd",
  ENA_USD: "0xb7910ba7322db020416fcac28b48c01212fd9cc8fbcbaf7d30477ed8605f6bd4",
  PEPE_USD: "0xd69731a2e74ac1ce884fc3890f7ee324b6deb66147055249568869ed700882e4",
  LINK_USD: "0x8ac0c70fff57e9aefdf5edf44b51d62c2d433653cbb2cf5cc06bb115af04d221",
  WIF_USD: "0x4ca4beeca86f0d164160323817a4e42b10010a724c2217c6ee41b54cd4cc61fc",
  WLD_USD: "0xd6835ad1f773de4a378115eb6824bd0c0e42d84d1c84d9750e853fb6b6c7794a",
  STRK_USD: "0x6a182399ff70ccf3e06024898942028204125a819e519a335ffa4579e66cd870",
  INJ_USD: "0x7a5bc1d2b56ad029048cd63964b3ad2776eadf812edc1a43a31406cb54bff592",
  MANTA_USD: "0xc3883bcf1101c111e9fcfe2465703c47f2b638e21fef2cce0502e6c8f416e0e2",
  SEI_USD: "0x53614f1cb0c031d4af66c04cb9c756234adad0e1cee85303795091499a4084eb",
  AVAX_USD: "0x93da3352f9f1d105fdfe4971cfa80e9dd777bfc5d0f683ebb6e1294b92137bb7",
  BLUR_USD: "0x856aac602516addee497edf6f50d39e8c95ae5fb0da1ed434a8c2ab9c3e877e9",
  MEME_USD: "0xcd2cee36951a571e035db0dfad138e6ecdb06b517cc3373cd7db5d3609b7927c",
  TIA_USD: "0x09f7c1d7dfbb7df2b8fe3d3d87ee94a2259d212da4f30c1f0540d066dfa44723",
  BNB_USD: "0x2f95862b045670cd22bee3114c39763a4a08beeb663b145d283c31d7d1101c4f",
  MATIC_USD: "0xa6a9e999d34b0c2a34c2cdc0a5f6922e375853109e2386426f8a9ec682692a6c",
  ARB_USD: "0x3fa4252848f9f0a1480be62745a4629d9eb1322aebab8a791e344b3b9c1adcf5",
  VIRTUAL_USD: "0x8132e3eb1dac3e56939a16ff83848d194345f6688bff97eb1c8bd462d558802b",
  JUP_USD:     "0x0a0408d619e9380abad35060f9192039ed5042fa6f82301d0e48bb52be830996",
  PYTH_USD:    "0x0bbf28e9a841a1cc788f6a361b17ca072d0ea3098a1e5df1c3922d06719579ff",
  W_USD:       "0xeff7446475e218517566ea99e72a4abec2e1bd8498b43b7d8331e29dcb059389",
  SUI_USD:     "0x23d7315113f5b1d3ba7a83604c44b94d79f4fd69af77f804fc7f920a6dc65744",
  TRUMP_USD:   "0x879551021853eec7a7dc827578e8e69da7e4fa8148339aa0d3d5296405be4b1a",
  SOL_USD:     "0xef0d8b6fda2ceba41da15d4095d1da392a0d2f8ed0c6c7bc0f4cfac8c280b56d",
  APT_USD:     "0x03ae4db29ed4ae33d323568895aa00337e658e348b37509f5372ae51f0af00d5",
  FARTCOIN_USD: "0x58cd29ef0e714c5affc44f269b2c1899a52da4169d7acc147b9da692e6953608",
  KAITO_USD: "0x7302dee641a08507c297a7b0c8b3efa74a48a3baa6c040acab1e5209692b7e59",
  EOS_USD: "0x06ade621dbc31ed0fc9255caaab984a468abe84164fb2ccc76f02a4636d97e31",
};

// Fetch latest price for PnL calc only (PYTH)
export async function fetchPrice(pair: string): Promise<number> {
  const url = `${process.env.PYTH_API_URL}?ids[]=${priceFeeds[pair]}`;
  const [data] = await fetch(url).then(r => r.json()) as any[];
  return Number(data.price.price) * 10 ** data.price.expo;
}

export async function getPositions({ client, account }: {
  client: MerkleClient;
  account: Account;
}): Promise<any[]> {
  return client.getPositions({ address: account.accountAddress.toString() });
}

export async function getTradingHistory({ client, account }: {
  client: MerkleClient;
  account: Account;
}): Promise<any[]> {
  return client.getTradingHistory({ address: account.accountAddress.toString() });
}

export async function getOrders({ client, account }: {
  client: MerkleClient;
  account: Account;
}): Promise<any[]> {
  return client.getOrders({ address: account.accountAddress.toString() });
}

export function resolveSymbolFromPairType(pairType: string): string | undefined {
  return Object.keys(priceFeeds).find(sym =>
    pairType.toUpperCase().includes(sym)
  );
}

export async function initBlockchain(encryptedPrivateKey: string) {

  let aptosConfig: AptosConfig;
  let config: MerkleClientConfig;

  if(process.env.NETWORK === 'mainnet') {
    aptosConfig = new AptosConfig({
      network: Network.MAINNET,
      fullnode: process.env.APTOS_NODE_URL,
    });
    config = await MerkleClientConfig.mainnet({
      aptosConfig
    });
  } else {
    aptosConfig = new AptosConfig({
      network: Network.TESTNET,
      fullnode: process.env.APTOS_NODE_URL,
    });
    config = await MerkleClientConfig.testnet({
      aptosConfig
    });
  }
  
  const client = new MerkleClient(config);
  const aptos = new Aptos(aptosConfig);

  // const config = await MerkleClientConfig.testnet();
  // const client = new MerkleClient(config);
  // const aptos = new Aptos(config.aptosConfig);

  const account = Account.fromPrivateKey({
    privateKey: new Ed25519PrivateKey(
      PrivateKey.formatPrivateKey(
        encryptedPrivateKey!, PrivateKeyVariants.Ed25519
      )
    )
  });
  console.log("✅ Blockchain initialized.");
  return { client, aptos, account };
}

// TRADE EXECUTION
export async function closePosition(
  pair: string,
  pos: any,
  client: MerkleClient,
  aptos: Aptos,
  account: Account,
  reason: string,
  price: number
) {
  for (let attempt = 1; attempt <= 2; attempt++) {
    try {
      // ✅ Re-fetch fresh position state
      const freshPositions = await client.getPositions({ address: account.accountAddress.toString() });
      const matched = freshPositions.find(p => p.pairType === pos.pairType) as any;

      if (!matched || BigInt(matched.size) === 0n) {
        console.warn(`⚠️ No open position found for ${pair}. Skipping close.`);
        return;
      }

      const sizeDelta = BigInt(matched.size);
      const isLong = matched.isLong ?? matched.side === "long";

      const payload = client.payloads.placeMarketOrder({
        pair,
        userAddress: account.accountAddress.toString(),
        sizeDelta,
        collateralDelta: 0n,
        isLong,
        isIncrease: false,
      }) as any;

      payload.functionArguments[10] = payload.functionArguments[10].toString();

      const txn = await aptos.transaction.build.simple({
        sender: account.accountAddress,
        data: payload,
      });

      const { hash } = await aptos.signAndSubmitTransaction({ signer: account, transaction: txn });
      await aptos.waitForTransaction({ transactionHash: hash });

      console.log(`✅ Closed ${pair} @ ${price.toFixed(6)} | Reason: ${reason} | TX: ${hash}`);
      return; // exit on success

    } catch (err: any) {
      console.error(`❌ Attempt ${attempt} to close ${pair} failed:`, err.message ?? err);
      if (attempt === 2) {
        console.error(`🚨 Giving up on closing ${pair} after 2 failed attempts.`);
        throw new Error(`Failed to close position for ${pair} after 2 attempts: ${err.message ?? err}`);
      } else {
        await new Promise(res => setTimeout(res, 2000));
      }
    }
  }
}

export async function closeAllPositions({ client, aptos, account, tradeId }: {
  client: MerkleClient;
  aptos: Aptos;
  account: Account;
  tradeId: number;
}): Promise<void> {
  console.log("🚪 Closing all positions...");
  const positions = await getPositions({ client, account }) as any[];
  for (const pos of positions) {
    if (BigInt(pos.size) > 0n) {
      const symbol = resolveSymbolFromPairType(pos.pairType);
      try {
        if (!symbol) continue;
        console.log(`🚪 Closing ${symbol}...`);
        const mark = await fetchPrice(symbol);
        await closePosition(symbol, pos, client, aptos, account, 'market_dry', mark);
        } catch (err) {
          console.warn(`⚠️ Failed to close ${symbol}:`, err);
          // throw new Error(`Failed to close position for ${symbol}: ${err instanceof Error ? err.message : err}`);
          await tradeService.createTradeLog({
            tradeId,
            text: `⚠️ Failed to close ${symbol}: ${err instanceof Error ? err.message : err}`,
            logType: 'trade',
          });
      }
    }
  }
  console.log("✅ All positions closed.");
}

// ───── MAIN FUNCTION ─────
export async function runSignalCheckAndOpen({
  tradeId,
  privateKey,
  symbol,
  perPositionBudget,
  leverage
}: {
  tradeId: number;
  privateKey: string;
  symbol: string;
  perPositionBudget: number;
  leverage: number;
}): Promise<{
  positionOpened: boolean;
  marketRegime: string;
  entryPrice: number;
  direction: 'long' | 'short';
  leverage: number;
  signalScore: number;
  rsi: number;
  macdHist: number;
  emaSlope: number;
  atrPct: number;
  txHash: string;
}> {
  try {
    const signal = await evaluateSignalOnly({ symbol, tradeId });
    const {
      shouldOpen,
      entryType,
      marketRegime,
      signalScore
    } = signal;

    console.log(`📊 ${symbol} | SignalScore: ${signalScore?.toFixed(2)} | Regime: ${marketRegime} | Decision: ${shouldOpen ? entryType : 'SKIP'}`);
    await tradeService.createTradeLog({
      tradeId,
      text: `📊 ${symbol} | SignalScore: ${signalScore?.toFixed(2)} | Regime: ${marketRegime} | Decision: ${shouldOpen ? entryType : 'SKIP'}`,
      logType: 'signal',
    });

    const fallbackReturn = {
      positionOpened: false,
      marketRegime,
      entryPrice: 0,
      direction: "long" as "long" | "short",
      leverage: 1,
      signalScore: 0,
      rsi: 0,
      macdHist: 0,
      emaSlope: 0,
      atrPct: 0,
      txHash: ''
    };

    if (!shouldOpen) {
      console.log(`❌ Skipping ${symbol}: No strong entry signal.`);
      await tradeService.createTradeLog({
        tradeId,
        text: `❌ Skipping ${symbol}: No strong entry signal.`,
        logType: 'signal',
      });
      return { ...fallbackReturn, marketRegime: marketRegime ?? 'unknown' };
    }

    const ohlcv = await fetchOHLCVFromBinance({ symbol, interval: '5m', limit: 100 });
    const entryPrice = ohlcv.close[ohlcv.close.length - 1];

    const isLong = entryType === 'long';

    console.log(`🚀 Executing ${symbol} | ${isLong ? 'LONG' : 'SHORT'} | Entry: ${entryPrice.toFixed(6)} | Lev: ${leverage}x`);
    await tradeService.createTradeLog({
      tradeId,
      text: `🚀 Executing ${symbol} | ${isLong ? 'LONG' : 'SHORT'} | Entry: ${entryPrice.toFixed(6)} | Lev: ${leverage}x`,
      logType: 'info',
    });

    const response = await openMarketOrder(privateKey, symbol, leverage, perPositionBudget, isLong) as any;
    if(!response.success) {
      console.error(`❌ Failed to open position for ${symbol}:`, response.error);
      await tradeService.createTradeLog({
        tradeId,
        text: `❌ Failed to open position for ${symbol}: ${response.error}`,
        logType: 'trade',
      });
      return { ...fallbackReturn, marketRegime: marketRegime ?? 'unknown' };
    }

    const { hash } = response;

    console.log(`✅ Opened ${symbol} | ${isLong ? 'LONG' : 'SHORT'} | TX: ${hash}`);
    await tradeService.createTradeLog({
      tradeId,
      text: `✅ Opened ${symbol} | ${isLong ? 'LONG' : 'SHORT'} | TX: ${hash}`,
      logType: 'trade',
    });

    const signalResult = checkSignals(symbol, ohlcv);
    const closePrice = ohlcv.close[ohlcv.close.length - 1];
    const indicators = buildIndicatorInputs(signalResult, closePrice);
    const { rsi, macdHist, emaSlope, atrPct } = indicators;

    return {
      positionOpened: true,
      marketRegime: marketRegime ?? 'unknown',
      entryPrice,
      direction: isLong ? 'long' : 'short',
      leverage,
      signalScore: signalScore ?? 0,
      rsi,
      macdHist,
      emaSlope,
      atrPct,
      txHash: hash
    };

  } catch (err) {
    console.error(`❌ Failed to open position for ${symbol}:`, err);
    await tradeService.createTradeLog({
      tradeId,
      text: `❌ Failed to open position for ${symbol}: ${err instanceof Error ? err.message : err}`,
      logType: 'trade',
    });

    return {
      positionOpened: false,
      marketRegime: 'neutral',
      entryPrice: 0,
      direction: 'long',
      leverage: 1,
      signalScore: 0,
      rsi: 0,
      macdHist: 0,
      emaSlope: 0,
      atrPct: 0,
      txHash: ''
    };
  }
}
/*
 * Returns the total unrealized PnL (USD) across all active positions.
 */
export async function getTotalPnL(
  client: MerkleClient,
  account: Account,
  tradeId: number
): Promise<number> {

  const positions = await client.getPositions({
    address: account.accountAddress.toString(),
  }) as any[];
  console.log(positions);
  let totalPnl = 0;

  for (const pos of positions) {
    if (BigInt(pos.size) === 0n) continue;

    const symbol = resolveSymbolFromPairType(pos.pairType);
    if (!symbol) {
      console.warn(`⚠️ No matching symbol for pairType: ${pos.pairType}`);
      await tradeService.createTradeLog({
        tradeId,
        text: `⚠️ No matching symbol for pairType: ${pos.pairType}`,
        logType: 'pnl',
      });
      continue;
    }

    // fallback through all possible entry fields
    const rawEntry =
      pos.avgPrice ??
      (pos as any).entry ??
      (pos as any).entryPrice ??
      0;
    const entry = Number(rawEntry) / 1e10;
    if (!entry || isNaN(entry)) {
      console.warn(`⚠️ ${symbol} missing entry price, rawEntry=${rawEntry}`);
      await tradeService.createTradeLog({
        tradeId,
        text: `⚠️ ${symbol} missing entry price, rawEntry=${rawEntry}`,
        logType: 'pnl',
      });
      continue;
    }

    // ALWAYS fetch the live mark price
    const mark = await fetchPrice(symbol);

    const sizeUsd = Number(pos.size) / 1e6;
    const isLong = pos.isLong ?? pos.side === "long";
    const dir = isLong ? 1 : -1;

    // USD PnL
    const pnl = ((mark - entry) / entry) * dir * sizeUsd;

    console.log(
      `📈 ${symbol} | ${isLong ? "LONG" : "SHORT"} | Entry=${entry.toFixed(
        6
      )} Mark=${mark.toFixed(6)} → PnL=$${pnl.toFixed(4)}`
    );
    await tradeService.createTradeLog({
      tradeId,
      text: `📈 ${symbol} | ${isLong ? "LONG" : "SHORT"} | Entry=${entry.toFixed(
        6
      )} Mark=${mark.toFixed(6)} → PnL=$${pnl.toFixed(4)}`,
      logType: 'pnl',
    });

    totalPnl += pnl;
  }

  return totalPnl;
}
