import dotenv from 'dotenv';
import {
  initBlockchain,
  getTotalPnL,
  closeAllPositions,
  getPositions,
  getTradingHistory,
  runSignalCheckAndOpen,
  closePosition,
  priceFeeds,
} from './blockchain';
import { checkAndCloseForTP } from './strategyEngine';
import * as tradeService from '@/services/trade';
import * as agentService from '@/services/agent';
import * as chatService from '@/services/chat';
import decryptPrivateKey from '@/utils/blockchain/cryptography/decryptPrivateKey';
import { POSITION_STATUS, TRADE_STATUS } from '@/db/schemas/constants/trade';
import getTrendingTokens from './getTrendingTokens';

dotenv.config();

// Global variable to track currently running trades
const runningTrades = new Set<number>();

function calculateLeverage(budget: number, profitGoal: number): number {
  // 1) Compute ROI
  const ROI: number = profitGoal / budget;

  // 2) Define the ROI range over which you want linear scaling
  const minROI: number = 0.1;
  const maxROI: number = 5.0;

  // 3) Define the leverage bounds
  const minLeverage: number = 15;
  const maxLeverage: number = 130;

  // 4) Clamp the ROI into [minROI, maxROI]
  const clampedROI: number = Math.min(maxROI, Math.max(minROI, ROI));

  // 5) Linearly map clampedROI ∈ [minROI, maxROI] → [minLeverage, maxLeverage]
  //    t = (clampedROI – minROI) / (maxROI – minROI)
  const t: number = (clampedROI - minROI) / (maxROI - minROI);

  //    leverage = minLeverage + t * (maxLeverage – minLeverage)
  const leverage: number = minLeverage + t * (maxLeverage - minLeverage);

  return Math.max(30, leverage);
}

async function updateOpenPositions(trade: any, agent: any, client: any, account: any) {
  const openPositions = await tradeService.getPositionsByTradeId(trade.id, POSITION_STATUS.OPEN);
  const rawPositions = await getPositions({ client, account });
  const currentOpen = new Set(
    rawPositions.filter(p => BigInt(p.size) !== 0n).map(p =>
      Object.keys(priceFeeds).find(sym => p.pairType.toUpperCase().includes(sym))!
    )
  );
  const history = await getTradingHistory({ client, account });

  for (const position of openPositions) {
    if (position.pair && !currentOpen.has(position.pair)) {
      const pairState = history.find(p => p.pairType.includes(position.pair) && ['position_liquidate', 'position_close'].includes(p.eventType));

      // Add detailed logging for position closure
      console.log(`🔍 Position ${position.pair} no longer open. Checking history...`);
      await tradeService.createTradeLog({
        tradeId: trade.id,
        text: `🔍 Position ${position.pair} no longer open. Checking history...`,
        logType: 'info',
      });

      if (pairState) {
        console.log(`📋 Found event for ${position.pair}: ${pairState.eventType} | PnL: $${pairState.pnlWithoutFee} | Price: ${pairState.price}`);
        await tradeService.createTradeLog({
          tradeId: trade.id,
          text: `📋 Found event for ${position.pair}: ${pairState.eventType} | PnL: $${pairState.pnlWithoutFee} | Price: ${pairState.price}`,
          logType: 'info',
        });
      } else {
        console.log(`⚠️ No history event found for ${position.pair} - position closed without clear reason`);
        await tradeService.createTradeLog({
          tradeId: trade.id,
          text: `⚠️ No history event found for ${position.pair} - position closed without clear reason`,
          logType: 'warning',
        });
      }

      const isLiquidated = pairState?.eventType === 'position_liquidate';
      const newStatus = isLiquidated ? POSITION_STATUS.LIQUIDATED : POSITION_STATUS.CLOSED;

      console.log(`📝 Updating position ${position.pair} status to: ${newStatus}`);
      await tradeService.createTradeLog({
        tradeId: trade.id,
        text: `📝 Updating position ${position.pair} status to: ${newStatus}`,
        logType: 'info',
      });

      await tradeService.updatePosition(position.id, {
        status: newStatus,
        pnl: pairState?.pnlWithoutFee ?? 0,
        exitPrice: pairState?.price,
      });

      await chatService.addTradeChat({
        conversationId: trade.conversationId!,
        message: `Position for ${position.pair} ${isLiquidated ? 'liquidated' : 'closed' }. PnL: $${pairState?.pnlWithoutFee ?? 0}`,
        userId: agent.userId,
        agentId: agent.id,
        data: {
          tradeId: trade.id,
          pair: position.pair,
          pnl: pairState?.pnlWithoutFee ?? 0,
          exitPrice: pairState?.price,
          eventType: pairState?.eventType,
        }
      });
    }
  }
  const positions = await tradeService.getPositionsByTradeId(trade.id);
  const newOpenPositions = positions.filter(p => p.status === POSITION_STATUS.OPEN);
  const closedPositions = positions.filter(p => p.status !== POSITION_STATUS.OPEN);

  if (closedPositions.length && !newOpenPositions.length) {
    const totalAcquiredPnl = await tradeService.getTotalPnlPerTrade(trade.id);
    await tradeService.updateTrade(trade.id, {
      status: TRADE_STATUS.CLOSED,
      totalPnl: totalAcquiredPnl.toString(),
    });
    
    console.log(`❌ No open positions. Closing trade ${trade.id}`);
    await tradeService.createTradeLog({
      tradeId: trade.id,
      text: `❌ No open positions. Closing trade ${trade.id}`,
      logType: 'error',
    });
    await chatService.addTradeChat({
      conversationId: trade.conversationId!,
      message: `Trade successfully completed with. Total PnL: $${totalAcquiredPnl.toString(2)}`,
      userId: agent.userId,
      agentId: agent.id,
      data: {
        tradeId: trade.id,
        totalPnl: totalAcquiredPnl,
        isSuccess: totalAcquiredPnl > 0,
      }
    });
    await tradeService.createTradeLog({
      tradeId: trade.id,
      text: `Trade successfully completed with. Total PnL: $${totalAcquiredPnl.toString(2)}`,
      logType: totalAcquiredPnl > 0 ? 'success' : 'error',
    })
    return { isClosed: true };
  }
  return { isClosed: false };
}

async function handleTrade(tradeId: number) {
  // Check if trade is already running
  
  if (runningTrades.has(tradeId)) {
    console.log(`⏭️ Trade ${tradeId} is already running, skipping...`);
    await tradeService.createTradeLog({
      tradeId,
      text: `⏭️ Trade ${tradeId} is already running, skipping...`,
      logType: 'success',
    });
    return;
  }

  // Add trade to running trades
  runningTrades.add(tradeId);
  console.log(`🔄 Running trade ${tradeId}...`);
  await tradeService.createTradeLog({
    tradeId,
    text: `🔄 ********* Monitoring trade ${tradeId} *********`,
    logType: 'success',
  });

  try {
    const trade = await tradeService.getTradeById(tradeId);
    if (!trade) {
      console.error(`Trade with ID ${tradeId} not found.`);
      runningTrades.delete(tradeId); // Remove from running trades
      await tradeService.createTradeLog({
        tradeId,
        text: `❌ Trade with ID ${tradeId} not found.`,
        logType: 'error',
      });
      return;
    }

    const agent = await agentService.getFullAgentById(trade?.agentId ?? 0);
    if (!agent) {
      console.error(`Agent with ID ${trade?.agentId} not found.`);
      runningTrades.delete(tradeId); // Remove from running trades
      await tradeService.createTradeLog({
        tradeId,
        text: `❌ Agent with ID ${trade?.agentId} not found.`,
        logType: 'error',
      });
      return;
    }

    if (!agent.privateKey) {
      console.error(`Agent with ID ${trade?.agentId} has no private key.`);
      runningTrades.delete(tradeId); // Remove from running trades
      await tradeService.createTradeLog({
        tradeId,
        text: `❌ Agent with ID ${trade?.agentId} has no private key.`,
        logType: 'error',
      });
      return;
    }

    const allPositions = await tradeService.getPositionsByTradeId(tradeId);
    const openPositions = allPositions.filter(p => p.status === POSITION_STATUS.OPEN);

    const encryptedPrivateKey = decryptPrivateKey(agent.privateKey);

    const { client, aptos, account } = await initBlockchain(encryptedPrivateKey);

    const budget = trade?.budget ?? 50;
    const profitGoal = trade?.profitGoal ?? 10;
    let maxPositions = budget > 100 ? 6 : budget > 50 ? 4 : 2;
    const perPositionBudget = budget / maxPositions;
    let allowedPositions = maxPositions - allPositions.length;

    await tradeService.createTradeLog({
      tradeId,
      text: `Total allowed positions: ${allowedPositions} for budget is ${budget}`,
      logType: 'info',
    });

    let totalPnl = await tradeService.getTotalPnlOfClosedTrade(tradeId) || 0;
    
    let positionsOpened = openPositions.length;

    const currentTotalPnl = await getTotalPnL(client, account, tradeId);
    totalPnl += currentTotalPnl;

    console.log(`💰 Total PnL: $${totalPnl.toFixed(2)} / $${profitGoal}`);
    await tradeService.createTradeLog({
      tradeId,
      text: `💰 Total PnL: $${totalPnl.toFixed(2)} / $${profitGoal}`,
      logType: 'info',
    });

    const { isClosed } = await updateOpenPositions(trade, agent, client, account);
    if (isClosed) {
      runningTrades.delete(tradeId); // Remove from running trades
      return;
    }

    if (totalPnl >= profitGoal) {
      await closeAllPositions({ client, aptos, account, tradeId });

      // sleep for 10 seconds to allow for all positions to close
      await new Promise(resolve => setTimeout(resolve, 10000));

      await updateOpenPositions(trade, agent, client, account);
      console.log(`🎯 Profit goal reached. Closing all.`);
      await tradeService.createTradeLog({
        tradeId,
        text: `🎯 Profit goal reached. Closing all.`,
        logType: 'success',
      });
      runningTrades.delete(tradeId); // Remove from running trades
      return;
    }

    console.log(`💼 Open positions: ${openPositions.length}`);
    await tradeService.createTradeLog({
      tradeId,
      text: `💼 Open positions: ${openPositions.length}`,
      logType: 'info',
    });

    const leverage = calculateLeverage(budget, profitGoal);

    if (!positionsOpened || allowedPositions > 0) {

      const trendingTokens = await getTrendingTokens();
      const takenPositionSymbols = allPositions.map(p => p.pair);
      const allowedTokens = trendingTokens.filter(t => !takenPositionSymbols.includes(t));

      console.log(`✅ Tokens to watch: ${allowedTokens.join(', ')}`);
      await tradeService.createTradeLog({
        tradeId,
        text: `✅ Tokens to watch: ${allowedTokens.join(', ')}`,
        logType: 'info',
      });
    
      for (const symbol of allowedTokens) {
        const ifPositionExists = openPositions.find(p => p.pair === symbol);
        if (ifPositionExists) continue;

        const opened = await runSignalCheckAndOpen({
          tradeId,
          privateKey: encryptedPrivateKey,
          symbol,
          perPositionBudget,
          leverage
        });

        if (opened?.positionOpened) {
          await tradeService.createPosition({
            tradeId,
            pair: symbol,
            budget: perPositionBudget,
            entryPrice: opened.entryPrice,
            marketRegime: opened.marketRegime,
            direction: opened.direction,
            leverage: opened.leverage,
            signalScore: opened.signalScore,
            rsi: opened.rsi,
            macdHist: opened.macdHist,
            emaSlope: opened.emaSlope,
            atrPct: opened.atrPct,
            txHash: opened.txHash,
            status: 'open',
          });

          positionsOpened++;

          console.log(`🚀 Opened position for ${symbol}`);
          await tradeService.createTradeLog({
            tradeId,
            text: `🚀 Opened position for ${symbol}`,
            logType: 'info',
          });

          await chatService.addTradeChat({
            conversationId: trade.conversationId!,
            message: `🚀 Opened position for ${symbol} at $${opened.entryPrice.toFixed(2)} with leverage ${opened.leverage}`,
            userId: agent.userId,
            agentId: agent.id,
            data: {
              symbol,
              tradeId,
              entryPrice: opened.entryPrice,
              leverage: opened.leverage,
              collateral: perPositionBudget,
              direction: opened.direction,
            }
          })

          if (positionsOpened >= allowedPositions) break;
        }
      }
    }
    runningTrades.delete(tradeId);
  } catch (error) {
    const message = error instanceof Error ? error.message : String(error);
    const stack = error instanceof Error ? error.stack : 'No stack trace available';

    console.error(`Error running trade ${tradeId}: ${message}\nStack: ${stack}`);

    await tradeService.createTradeLog({
      tradeId,
      text: `❌ Error running trade: ${message}\nStack Trace:\n${stack}`,
      logType: 'error',
    });
    runningTrades.delete(tradeId);
  }
}

export default handleTrade;
