import db from '@/config/db';
import { chats, conversations, agents } from '@/db/schemas';
import { trades } from '@/db/schemas/trade';
import { eq, and, asc, desc, count, sql } from 'drizzle-orm';
import { getTableColumns } from 'drizzle-orm';
import {
  IChat,
  IConversation,
  IConversationWithAgent
} from '@/db/schemas/chat';
import { ConversationType } from '@/db/helpers/constant';

const getAgentColumns = () => {
  const { privateKey, ...rest } = getTableColumns(agents);
  const booleanColumns = Object.keys(rest).reduce(
    (acc, key) => {
      acc[key] = true;
      return acc;
    },
    {} as Record<string, boolean>,
  );
  return booleanColumns;
};

// Fetch all chats
export const getAllChats = async (): Promise<IChat[]> => {
  return await db.query.chats.findMany({
    orderBy: [asc(chats.createdAt)], // Order by creation time
  });
};

// Create Conversation
// Note: conversationType parameter is commented out as it's not in the schema yet
export const createConversation = async (
  agentId: number,
  userId: number,
  // conversationType: ConversationType = ConversationType.TRADE,
  title?: string,
): Promise<IConversation | null> => {
  const result = await db
    .insert(conversations)
    .values({ agentId, userId, title })
    .returning();
  return result[0];
};

// Fetch conversation by userId
export const getConversationByUserId = async (
  userId: number,
): Promise<IConversation | null> => {
  const result = await db.query.conversations.findFirst({
    where: eq(conversations.userId, userId),
  });
  return result ?? null;
};

// Fetch conversation by agentId and userId
export const getConversationByAgentIdAndUserId = async (
  agentId: number,
  userId: number,
): Promise<any | null> => {
  const conversation = await db.query.conversations.findFirst({
    where: and(eq(conversations.agentId, agentId), eq(conversations.userId, userId)),
    with: {
      agent: {
        columns: getAgentColumns(),
      },
      chats: {
        // Order by `createdAt` descending so newest chats come first
        orderBy: table => [desc(table.createdAt)],
        limit: 10, // Return only the latest 10
      },
    },
  });

  return conversation ?? null;
};

// Fetch conversation by conversation ID
export const getConversationById = async (
  userId: number,
  id: number,
): Promise<IConversationWithAgent | null> => {
  const conversation = await db.query.conversations.findFirst({
    where: and(eq(conversations.userId, userId), eq(conversations.id, id)),
    with: {
      agent: true,
    },
  });

  return conversation ?? null;
};

// Fetch conversation by conversation ID with chats
export const getConversationByIdWithChats = async (
  id: number,
): Promise<any | null> => {
  const conversation = await db.query.conversations.findFirst({
    where: eq(conversations.id, id),
    with: {
      agent: true,
      chats: {
        where: table => {
          return and(
            sql`length(${table.message}) < 300`, // Filter by message length
          );
        },
        orderBy: table => [desc(table.createdAt)], // Order by creation time (newest first)
        limit: 20, // Return only the latest 20
      },
    },
  });

  return conversation ?? null;
};

// Fetch chats by conversation ID with pagination
export const getChatsByConversationIdWithPagination = async (
  conversationId: number,
  page: number = 1, // Default to page 1
  limit: number = 10, // Default to 10 items per page
): Promise<{
  chats: (IChat & { tradeId?: number; conversationTitle?: string })[];
  totalItems: number;
  totalPages: number;
  currentPage: number;
  itemsPerPage: number;
}> => {
  const offset = (page - 1) * limit; // Calculate the offset

  // Fetch the chat messages with conversation info
  const chatResults = await db.query.chats.findMany({
    where: eq(chats.conversationId, conversationId),
    orderBy: [asc(chats.createdAt)], // Order by creation time
    limit: limit, // Number of items to fetch
    offset: offset, // Number of items to skip
    with: {
      conversation: {
        columns: {
          title: true,
        },
      },
    },
  });

  // Fetch the total number of chat messages in the conversation
  const totalItems = await db
    .select({ count: count() })
    .from(chats)
    .where(eq(chats.conversationId, conversationId))
    .then(res => res[0]?.count ?? 0);

  // Calculate the total number of pages
  const totalPages = Math.ceil(totalItems / limit);

  // For each chat, check if there's a trade associated with it and add conversationTitle
  const chatsWithTradeInfo = await Promise.all(
    chatResults.map(async (chat) => {
      // Find any trade that references this chat
      const trade = await db.query.trades.findFirst({
        where: eq(trades.chatId, chat.id),
        columns: {
          id: true,
        },
      });

      return {
        ...chat,
        tradeId: trade?.id,
        conversationTitle: chat.conversation?.title ?? undefined,
      };
    })
  );

  return {
    chats: chatsWithTradeInfo,
    totalItems,
    totalPages,
    currentPage: page,
    itemsPerPage: limit,
  };
};

// Fetch all chats sent by a specific user or agent
export const getChatsBySenderId = async (senderId: number): Promise<IChat[]> => {
  return await db.query.chats.findMany({
    where: eq(chats.senderId, senderId),
    orderBy: [asc(chats.createdAt)], // Order by creation time
  });
};

// Fetch all chats received by a specific user or agent
export const getChatsByReceiverId = async (receiverId: number): Promise<IChat[]> => {
  return await db.query.chats.findMany({
    where: eq(chats.receiverId, receiverId),
    orderBy: [asc(chats.createdAt)], // Order by creation time
  });
};

// Create a new chat message
export const createChat = async (data: {
  conversationId: number;
  senderId: number;
  receiverId: number;
  message: string;
  messageType: string;
  isAgent: boolean;
  data?: any; // Optional additional data
}): Promise<IChat> => {
  const result = await db.insert(chats).values(data).returning();
  return result[0];
};

// Update a chat message
export const updateChat = async (id: number, message: string): Promise<IChat | null> => {
  const result = await db.update(chats).set({ message }).where(eq(chats.id, id)).returning();
  return result[0];
};

// Delete a chat message
export const deleteChat = async (id: number): Promise<void> => {
  await db.delete(chats).where(eq(chats.id, id));
};

// Fetch all chats between a sender and receiver in a specific conversation
export const getChatsBySenderAndReceiver = async (
  conversationId: number,
  senderId: number,
  receiverId: number,
): Promise<IChat[]> => {
  return await db.query.chats.findMany({
    where: and(
      eq(chats.conversationId, conversationId),
      eq(chats.senderId, senderId),
      eq(chats.receiverId, receiverId),
    ),
    orderBy: [asc(chats.createdAt)], // Order by creation time
  });
};

/**
 * Get conversations with pagination
 * Note: conversationType parameter is commented out as it's not in the schema yet
 */
export const getConversationWithPagination = async (
  userId: number,
  agentId: number,
  page: number = 1,
  limit: number = 10,
) => {
  const offset = (page - 1) * limit;
  return await db.query.conversations.findMany({
    where: and(
      eq(conversations.agentId, agentId),
      eq(conversations.userId, userId)
    ),
    limit: limit,
    offset: offset,
    orderBy: [desc(conversations.createdAt)],
    with: {
      chats: {
        orderBy: table => [desc(table.createdAt)],
        limit: 10,
      },
    },
  });
};

export const getLatestConversation = async (userId: number, _conversationType: ConversationType) => {
  const conversation = await db.query.conversations.findFirst({
    where: and(
      eq(conversations.userId, userId)
    ),
    orderBy: [desc(conversations.createdAt)],
    with: {
      chats: {
        orderBy: table => [desc(table.createdAt)],
        limit: 1, // Just fetch 1 chat to check if any exist
      },
    },
  });

  return conversation;
};

/**
 * Create a new message with additional data
 */
export const createMessage = async (data: {
  conversationId: number;
  senderId: number;
  receiverId: number;
  message: string;
  messageType: string;
  isAgent: boolean;
  data: any;
}): Promise<IChat> => {
  const result = await db.insert(chats).values(data).returning();
  return result[0];
};


export const addTradeChat = async (params: {
  conversationId: number;
  message: string;
  userId: number;
  agentId: number;
  data: any;
}): Promise<IChat> => {
  const { conversationId, message, userId, agentId, data } = params;
  const result: IChat[] = await db.insert(chats).values({
    conversationId,
    senderId: agentId,
    receiverId: userId,
    message: message,
    messageType: 'trade',
    isAgent: true,
    data
  }).returning();
  return result[0];
};

export const updateConversationStatus = async (
  conversationId: number,
  isPublic: boolean
): Promise<IConversation | null> => {
  const result = await db.update(conversations)
    .set({ isPublic })
    .where(eq(conversations.id, conversationId))
    .returning();

  return result[0] || null;
}

export const makeConversationPrivate = async (conversationId: number): Promise<IConversation | null> => {
  const result = await db.update(conversations)
    .set({ isPublic: false })
    .where(eq(conversations.id, conversationId))
    .returning();
  
  return result[0] || null;
}

export const getConversationObjByConversationId = async (conversationId: number): Promise<IConversation | null> => {
  const conversation = await db.query.conversations.findFirst({
    where: eq(conversations.id, conversationId)
  });

  return conversation ?? null;
}