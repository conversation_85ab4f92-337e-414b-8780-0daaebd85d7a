import { Request, Response } from 'express';
import * as chatService from '@/services/chat';

/**
 * Public endpoint to get conversation messages by conversationId
 * Only works if the conversation is public
 * @param req Request object with conversationId in params
 * @param res Response object
 */
const publicList = async (req: Request, res: Response): Promise<void> => {
  try {
    const conversationId = Number(req.params.conversationId);
    const page = Number(req.query.page) || 1;
    const limit = Number(req.query.limit) || 10;

    if (isNaN(conversationId)) {
      res.status(400).json({ message: 'Invalid conversation ID' });
      return;
    }

    // Fetch the conversation by ID to check if it's public
    const conversation = await chatService.getConversationObjByConversationId(conversationId);
    
    if (!conversation) {
      res.status(404).json({ message: 'Conversation not found' });
      return;
    }

    // Check if the conversation is public
    if (!conversation.isPublic) {
      res.status(403).json({ message: 'Forbidden: This conversation is not public' });
      return;
    }

    // Fetch paginated chats for the conversation
    const data = await chatService.getChatsByConversationIdWithPagination(
      conversationId,
      page,
      limit,
    );

    res.status(200).json(data);
    return;
  } catch (error) {
    console.error('Error in publicList:', error);
    res.status(500).json({
      message: 'Internal server error',
      error: error instanceof Error ? error.message : 'Unknown error occurred',
    });
    return;
  }
};

export default publicList;
