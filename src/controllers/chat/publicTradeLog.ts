import { Request, Response } from 'express';
import * as tradeService from '@/services/trade';

/**
 * Public endpoint to get logs for a trade by tradeId
 * Only works if the associated conversation is public
 * @param req Request object with tradeId in params
 * @param res Response object
 */
const publicTradeLog = async (req: Request, res: Response): Promise<void> => {
  try {
    const tradeId = Number(req.params.tradeId);

    if (isNaN(tradeId)) {
      res.status(400).json({ message: 'Invalid trade ID' });
      return;
    }

    // Get the trade with chat and conversation information
    const trade = await tradeService.getTradeWithChatAndConversation(tradeId);

    if (!trade) {
      res.status(404).json({ message: 'Trade not found' });
      return;
    }

    // Check if the associated conversation is public
    if (!trade.isPublic) {
      res.status(403).json({ message: 'Forbidden: This trade is not from a public conversation' });
      return;
    }

    // Get the logs for the trade
    const logs = await tradeService.getLogsByTradeIdOnly(tradeId, true);

    res.status(200).json({ logs: logs });
    return;
  } catch (error) {
    console.error('Error fetching public trade logs:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch trade logs',
      error: error instanceof Error ? error.message : 'Unknown error',
    });
  }
};

export default publicTradeLog;
