import { Request, Response } from 'express';
import * as tradeService from '@/services/trade';

/**
 * Get logs for a trade by tradeId
 * @param req Request object with tradeId in params
 * @param res Response object
 */
const getTradeLog = async (req: Request, res: Response): Promise<void> => {
  try {
    // Get the user ID from the authenticated request
    const userId = req.user?.id;

    if (!userId) {
      res.status(401).json({ message: 'Unauthorized' });
      return;
    }

    const tradeId = Number(req.params.tradeId);

    if (isNaN(tradeId)) {
      res.status(400).json({ message: 'Invalid trade ID' });
      return;
    }

    // First verify that the trade exists and belongs to the user (authorization)
    const trade = await tradeService.getTradeByIdAndUserId(tradeId, userId);

    if (!trade) {
      // We don't distinguish between "not found" and "not authorized" for security reasons
      res.status(404).json({ message: 'Trade not found' });
      return;
    }

    // check if trade is public or not if not check if the user is authorized to access it
    if (trade.userId !== userId) {
      res.status(403).json({ message: 'Forbidden: You do not have access to this trade' });
      return;
    }

    // Now that we've verified authorization, get the logs by tradeId only
    const logs = await tradeService.getLogsByTradeIdOnly(tradeId, true);

    res.status(200).json({logs: logs});
    return;
  } catch (error) {
    console.error('Error fetching trade logs:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch trade logs',
      error: error instanceof Error ? error.message : 'Unknown error',
    });
  }
};

export default getTradeLog;
