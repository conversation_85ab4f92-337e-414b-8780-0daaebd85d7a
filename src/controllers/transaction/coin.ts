import { Request, Response } from 'express';
import * as userService from '@/services/user';
import * as agentService from '@/services/agent';
import { AIAgentMessage } from '@/types/common';
import { getNftData } from '@/utils/blockchain/indexer/getNftData';
import getCoinData from '@/utils/blockchain/indexer/getCoinData';
import decryptPrivateKey from '@/utils/blockchain/cryptography/decryptPrivateKey';
import {
  SwapParams,
  swapTokensWithPanoraApi,
  transferTokens,
} from '@/utils/blockchain/transaction/coinTxnBuilder';
import { getAllLatestPositionHistory } from '@/db/indexer/trade';
import { buyAgentTokens } from '@/utils/blockchain/transaction/agentTxnBuilder';
import {
  fetchDCAOrderActivityWithId,
  getAllSnipeAndDcaSwapActivity,
  getSnipeAndDcaSwapActivity,
  insertDCARecord,
  setDCARecordClosed,
} from '@/db/indexer/dca';
import { getMinutesFromFrequency } from '@/utils/cron-job/tasks/transactions/utils';
import {
  getSnipeOrderActivityWithId,
  getSnipeOrderWithId,
  insertSwapActivities,
} from '@/db/indexer/snipe';
import { syncAgentTokenTransfers } from '@/db/indexer/agentTokenTransfers';

export const buyAgent = async (req: Request, res: Response): Promise<void> => {
  try {
    const { reserve_address, amount, contract_address } = req.body;

    if (!req.user?.id) {
      res.status(401).json({ error: 'Unauthorized' });
      return;
    }
    const user = (await userService.getUserById(req.user?.id)) as any;
    if (!user?.agent?.id) {
      res.status(400).json({ error: 'Agent not found' });
      return;
    }
    const agent = await agentService.getAgentDetail(user.agent.id);
    const privateKey = agent.privateKey;
    if (!privateKey) {
      res.status(400).json({ error: 'Agent wallet not found' });
      return;
    }
    const encryptedPrivateKey = decryptPrivateKey(privateKey);
    const result = await buyAgentTokens(
      encryptedPrivateKey,
      reserve_address,
      amount,
      contract_address,
    );
    res
      .status(200)
      .json({ message: 'Agent tokens bought successfully', data: { transactionRes: result } });
    return;
  } catch (error) {
    console.error('Error in buyAgentTokens:', error);
    res.status(500).json({
      message: 'Internal server error',
      error: error instanceof Error ? error.message : 'Unknown error occurred',
    });
    return;
  }
};

export const swap = async (req: Request, res: Response): Promise<void> => {
  try {
    const { frequency } = req.body;
    const tokenInfo = JSON.stringify(req.body?.tokenInfo ?? {});

    if (!req.user?.id) {
      res.status(401).json({ error: 'Unauthorized' });
      return;
    }
    const swapData = {
      fromTokenAddress: req.body.fromTokenAddress,
      toTokenAddress: req.body.toTokenAddress,
      fromTokenAmount: req.body.fromTokenAmount,
      toWalletAddress: req.body.toWalletAddress,
      slippagePercentage: (req.body.slippagePercentage ?? 1).toString(),
    };
    const user = (await userService.getUserById(req.user?.id)) as any;

    if (user.remainingPrompts < 1) {
      res.status(400).json({ error: 'Insufficient prompts' });
      return;
    }

    if (!user?.agent?.id) {
      res.status(400).json({ error: 'Agent not found' });
      return;
    }

    const agent = await agentService.getAgentDetail(user.agent.id);
    const privateKey = agent.privateKey;

    if (!privateKey) {
      res.status(400).json({ error: 'Agent wallet not found' });
      return;
    }
    const keysToValidate: (keyof typeof swapData)[] = [
      'fromTokenAddress',
      'toTokenAddress',
      'toWalletAddress',
    ];

    if (process.env.NETWORK === 'mainnet') {
      keysToValidate.forEach(key => {
        const value = swapData[key];
        if (!swapData[key] || !value.startsWith('0x')) {
          throw new Error(`Invalid ${key}`);
        }
      });
    }
    const encryptedPrivateKey = decryptPrivateKey(privateKey);
    const validatedSwapData: SwapParams = {
      fromTokenAddress: swapData.fromTokenAddress as `0x${string}`,
      toTokenAddress: swapData.toTokenAddress as `0x${string}`,
      fromTokenAmount: swapData.fromTokenAmount as `0x${string}`,
      toWalletAddress: swapData.toWalletAddress as `0x${string}`,
      slippagePercentage: swapData.slippagePercentage,
    };
    let swapResult;
    if (process.env.NETWORK === 'mainnet') {
      // swapResult = await swapTokens(validatedSwapData, encryptedPrivateKey);
      swapResult = await swapTokensWithPanoraApi(validatedSwapData, encryptedPrivateKey);
    }
    // else {
    //   swapResult = await swapTokensTest(validatedSwapData, encryptedPrivateKey, req.body.quote);
    // }
    if (swapResult?.success) {
      const minutes = getMinutesFromFrequency(frequency);
      if (minutes > 0) {
        await insertDCARecord({
          frequency,
          from_token: swapData.fromTokenAddress,
          to_token: swapData.toTokenAddress,
          amount: swapData.fromTokenAmount,
          quote: req.body.quote ?? null,
          creator_address: user.address,
          agent_address: user.agent.publicKey.toString(),
        });
      }
      await insertSwapActivities([
        {
          sender: swapResult.sender,
          transaction_hash: swapResult.hash,
          success: swapResult.success,
          from_token: swapData.fromTokenAddress,
          to_token: swapData.toTokenAddress,
          from_amount: Number(swapData.fromTokenAmount),
          to_amount: Number(swapResult.toTokenAmount),
          to_token_price_usd: swapResult.price,
          transaction_timestamp: swapResult.timestamp,
          order_id: null,
          source: 'NORMAL',
        },
      ]);
    }

    await userService.updateRemainingPrompts(user.id, user.remainingPrompts - 1);

    res
      .status(200)
      .json({ message: 'Tokens swapped successfully', data: { transactionRes: swapResult } });
    return;
  } catch (error) {
    console.error('Error in swap:\n', error);
    res.status(500).json({
      message: 'Internal server error',
      error: error instanceof Error ? error.message : 'Unknown error occurred',
    });
    return;
  }
};

export const transfer = async (req: Request, res: Response): Promise<void> => {
  try {
    const { amount, asset_address, receiver_address } = req.body;

    if (!req.user?.id) {
      res.status(401).json({ error: 'Unauthorized' });
      return;
    }
    const user = (await userService.getUserById(req.user?.id)) as any;

    if (!user?.agent?.id) {
      res.status(400).json({ error: 'Agent not found' });
      return;
    }

    const agent = await agentService.getAgentDetail(user.agent.id);
    const privateKey = agent.privateKey;

    if (!privateKey) {
      res.status(400).json({ error: 'Agent wallet not found' });
      return;
    }
    const encryptedPrivateKey = decryptPrivateKey(privateKey);
    const transactionRes = await transferTokens(
      amount,
      asset_address,
      receiver_address,
      encryptedPrivateKey,
    );
    res.status(200).json({
      message: 'Tokens transferred successfully',
      data: { transactionRes: transactionRes.hash },
    });

    return;
  } catch (error) {
    console.error('Error in transfer:', error);

    res.status(500).json({
      message: 'Internal server error',
      error: error instanceof Error ? error.message : 'Unknown error occurred',
    });

    return;
  }
};

export const closeDCAOrder = async (req: Request, res: Response): Promise<void> => {
  try {
    const { id } = req.params;
    if (!req.user?.id) {
      res.status(401).json({ error: 'Unauthorized' });
      return;
    }
    const user = (await userService.getUserById(req.user?.id)) as any;
    if (!user?.agent?.id) {
      res.status(400).json({ error: 'Agent not found' });
      return;
    }
    const agent = await agentService.getAgentDetail(user.agent.id);
    if (!agent) {
      res.status(400).json({ error: 'Agent not found' });
      return;
    }
    const address = agent.publicKey?.toString();
    if (!address) {
      res.status(400).json({ error: 'Agent wallet not found' });
      return;
    }
    await setDCARecordClosed(Number(id), address);
    res.status(200).json({ message: 'Order closed successfully' });
    return;
  } catch (error) {
    console.error('Error in closeDCAOrder:', error);
    res.status(500).json({
      message: 'Internal server error',
      error: error instanceof Error ? error.message : 'Unknown error occurred',
    });
    return;
  }
};

export const getSnipeAndDCAActivities = async (req: Request, res: Response): Promise<void> => {
  try {
    if (!req.user?.id) {
      res.status(401).json({ error: 'Unauthorized' });
      return;
    }

    // Get pagination parameters from request query
    const page = parseInt(req.query.page as string) || 1;
    const limit = parseInt(req.query.limit as string) || 10;

    // Validate pagination parameters
    if (page < 1 || limit < 1 || limit > 100) {
      res.status(400).json({ error: 'Invalid pagination parameters' });
      return;
    }

    const user = (await userService.getUserById(req.user?.id)) as any;
    if (!user?.agent?.id) {
      res.status(400).json({ error: 'Agent not found' });
      return;
    }

    const agent = await agentService.getAgentDetail(user.agent.id);
    if (!agent) {
      res.status(400).json({ error: 'Agent not found' });
      return;
    }

    const address = agent.publicKey?.toString();
    if (!address) {
      res.status(400).json({ error: 'Agent wallet not found' });
      return;
    }
    syncAgentTokenTransfers(address).catch(console.error);

    const data = await getSnipeAndDcaSwapActivity(address, page, limit);

    res.status(200).json({
      message: 'Activities fetched successfully',
      data,
    });

    return;
  } catch (error) {
    console.error('Error in getSnipeAndDCAActivities:', error);
    res.status(500).json({
      message: 'Internal server error',
      error: error instanceof Error ? error.message : 'Unknown error occurred',
    });
    return;
  }
};

export const getAllAggregatedActivities = async (req: Request, res: Response): Promise<void> => {
  try {
    const cursor = req.query.cursor as string | undefined; // needs to be a valid ISO timestamp string
    const limit = Number(req.query.limit) || 10;
    const parsedCursor = cursor && !isNaN(Date.parse(cursor)) ? cursor : undefined;

    const snipeAndSwapActivity = await getAllSnipeAndDcaSwapActivity(limit, parsedCursor);
    const tradeActivities = await getAllLatestPositionHistory(limit, cursor);
    const combined = [
      ...snipeAndSwapActivity.map(({ date, ...rest }) => ({
        ...rest,
        timestamp: new Date(date),
      })),
      ...tradeActivities.map(({ ts, ...rest }) => ({
        ...rest,
        timestamp: new Date(ts),
      })),
    ];

    const sortedActivities = combined.sort((a, b) => b.timestamp.getTime() - a.timestamp.getTime());
    res.status(200).json({
      message: 'Activities fetched successfully',
      data: { activities: sortedActivities },
    });
    return;
  } catch (error) {
    console.error('Error in getAllAggregatedActivities :', error);
    res.status(500).json({
      message: 'Internal server error',
      error: error instanceof Error ? error.message : 'Unknown error occurred',
    });
    return;
  }
};

export const getDCAOrders = async (req: Request, res: Response): Promise<void> => {
  try {
    const { order_id } = req.params;
    if (!req.user?.id) {
      res.status(401).json({ error: 'Unauthorized' });
      return;
    }
    const user = (await userService.getUserById(req.user?.id)) as any;
    if (!user?.agent?.id) {
      res.status(400).json({ error: 'Agent not found' });
      return;
    }
    const agent = await agentService.getAgentDetail(user.agent.id);
    if (!agent) {
      res.status(400).json({ error: 'Agent not found' });
      return;
    }
    const address = agent.publicKey?.toString();
    if (!address) {
      res.status(400).json({ error: 'Agent wallet not found' });
      return;
    }
    const results = await fetchDCAOrderActivityWithId(Number(order_id));

    res.status(200).json({ message: 'Order closed successfully', data: results });
    return;
  } catch (error) {
    console.error('Error in getDCAOrders:', error);
    res.status(500).json({
      message: 'Internal server error',
      error: error instanceof Error ? error.message : 'Unknown error occurred',
    });
    return;
  }
};
export const getSnipeOrder = async (req: Request, res: Response): Promise<void> => {
  try {
    const { order_id } = req.params;
    if (!req.user?.id) {
      res.status(401).json({ error: 'Unauthorized' });
      return;
    }
    const user = (await userService.getUserById(req.user?.id)) as any;
    if (!user?.agent?.id) {
      res.status(400).json({ error: 'Agent not found' });
      return;
    }
    const agent = await agentService.getAgentDetail(user.agent.id);
    if (!agent) {
      res.status(400).json({ error: 'Agent not found' });
      return;
    }
    const address = agent.publicKey?.toString();
    if (!address) {
      res.status(400).json({ error: 'Agent wallet not found' });
      return;
    }
    const results = await getSnipeOrderWithId(Number(order_id), address);

    res.status(200).json({ message: 'Order fetched successfully', data: results });
    return;
  } catch (error) {
    console.error('Error in getSnipeOrders:', error);
    res.status(500).json({
      message: 'Internal server error',
      error: error instanceof Error ? error.message : 'Unknown error occurred',
    });
    return;
  }
};
