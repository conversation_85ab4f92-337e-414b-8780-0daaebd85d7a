FROM ubuntu:24.04 AS python-base

# Update and install dependencies in one step
RUN apt-get update && \
    DEBIAN_FRONTEND=noninteractive apt-get install -y \
    software-properties-common curl vim-common python3 ca-certificates \
    libgtk2.0-0t64 libgtk-3-0t64 libgbm-dev libnotify-dev libnss3 \
    libxss1 libasound2t64 libxtst6 xauth xvfb

# Install Aptos CLI
RUN curl -fsSL "https://aptos.dev/scripts/install_cli.py" | python3

ENV PATH="/root/.local/bin:$PATH"
# Debug: Check Aptos CLI installation
FROM node:20 AS app-base
WORKDIR /
COPY package*.json ./
COPY . .
RUN npm install -g npm@10.9.0 tsx && \
    npm install --legacy-peer-deps && \
    npm install -D drizzle-kit && \
    npm install pg-format && \
    npm install tsx && \
    npm run build
COPY entrypoint.sh ./entrypoint.sh
RUN chmod +x ./entrypoint.sh
EXPOSE 8082
ENTRYPOINT [ "./entrypoint.sh" ]
