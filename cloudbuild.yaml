steps:
  - name: 'gcr.io/cloud-builders/docker'
    args: [
      'build',
      '-t', 'us-central1-docker.pkg.dev/true-strata-449506-a5/alura/alura-backend:$COMMIT_SHA',
      '.'
    ]

  - name: 'gcr.io/cloud-builders/docker'
    args: [
      'push',
      'us-central1-docker.pkg.dev/true-strata-449506-a5/alura/alura-backend:$COMMIT_SHA'
    ]

  - name: 'gcr.io/google.com/cloudsdktool/cloud-sdk'
    entrypoint: 'gcloud'
    args: [
      'run', 'deploy', 'alura-backend',
      '--image', 'us-central1-docker.pkg.dev/true-strata-449506-a5/alura/alura-backend:$COMMIT_SHA',
      '--region', 'us-central1',
      '--platform', 'managed',
      '--allow-unauthenticated'
    ]

images:
  - 'us-central1-docker.pkg.dev/true-strata-449506-a5/alura/alura-backend:$COMMIT_SHA'

options:
  logging: CLOUD_LOGGING_ONLY
