name: <PERSON>uild and Push Backend Service
description: Build and push the backend service Docker image to Google Artifact Registry
# This workflow is triggered on pushes to the deploy-dev branch.
# It builds the Docker image for the backend service and pushes it to Google Artifact Registry.
# The workflow uses Google Cloud authentication to access the Artifact Registry.
# Ensure that the GCP_SA_KEY secret is set in your GitHub repository settings.
on:
  push:
    branches:
      - staging
      - main

jobs:
  manual-build-and-push:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v3

      - name: Set uppercase branch name
        run: |
          echo "BRANCH_UPPER=${GITHUB_REF_NAME^^}" >>${GITHUB_ENV}
          echo "Branch name: ${GITHUB_REF_NAME}"
          echo "Uppercase branch name: ${GITHUB_REF_NAME^^}"

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v2

      - name: Authenticate to Google Cloud
        uses: google-github-actions/auth@v2
        with:
          credentials_json: ${{ secrets[format('G<PERSON>_SA_KEY_{0}', env.BR<PERSON>CH_UPPER)] }}
          project_id: ${{ secrets[format('PROJECT_ID_{0}', env.BRANCH_UPPER)] }}
          token_format: access_token
        env:
          SECRET_NAME_SA: GCP_SA_KEY_${{ env.BRANCH_UPPER }}
          SECRET_NAME_PROJECT: PROJECT_ID_${{ env.BRANCH_UPPER }}

      - name: Debug secret names
        run: |
          echo "Retrieving GCP Service Account Key from secret: $SECRET_NAME_SA"
          echo "Retrieving Project ID from secret: $SECRET_NAME_PROJECT"

      - name: Configure Docker for Artifact Registry
        run: |
          echo "Retrieving region from secret: REGION_${{ env.BRANCH_UPPER }}"
          gcloud auth configure-docker ${{ secrets[format('REGION_{0}', env.BRANCH_UPPER)] }}-docker.pkg.dev --quiet

      - name: Build and Push Backend Service
        run: |
          echo "Retrieving GAR location from secret: GAR_LOCATION_${{ env.BRANCH_UPPER }}"
          echo "Building and pushing backend service"
          docker build -t ${{ secrets[format('GAR_LOCATION_{0}', env.BRANCH_UPPER)] }}/alura-backend:latest ./python
          docker push ${{ secrets[format('GAR_LOCATION_{0}', env.BRANCH_UPPER)] }}/alura-backend:latest
